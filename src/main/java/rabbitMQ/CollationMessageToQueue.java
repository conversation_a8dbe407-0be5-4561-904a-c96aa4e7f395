package rabbitMQ;

import com.appnomic.appsone.common.protbuf.AnomalyEventProtos;
import com.rabbitmq.client.Channel;
import com.rabbitmq.client.Connection;
import com.rabbitmq.client.ConnectionFactory;
import com.rabbitmq.client.DeliverCallback;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.util.HashMap;
import java.util.Map;

public class CollationMessageToQueue {

    public static void main(String[] args) {
        Map<String, String> metadata = new HashMap<>();
        metadata.put("accountIdentifier", "heal_health");
        metadata.put("isRecurring", "false");
        metadata.put("lagDuration", "1");
        metadata.put("collationType", "OpenSearch");

        /*CollationInputProtos.CollationInput collationInput = CollationInputProtos.CollationInput.newBuilder()
                .putAllMetadata(metadata)
                .setRollupDetailsId(1)
                .setStartTime(1668160800000L)
                .setEndTime(1670767200000L)
                .build();*/

        AnomalyEventProtos.KpiInfo kpiInfo = AnomalyEventProtos.KpiInfo.newBuilder()
                .setKpiAttribute("ALL")
                .setInstanceId("RHEL_NB_App_Host_146_Inst_1-DR")
                .setKpiId("2")
                .setValue("8.04")
                .addSvcId("NB-App-Service-DR")
                //.putAllThresholds(thresholdMap)
                .setIsWorkload(false)
                .setThresholdSeverity("Severe")
                .putAllMetadata(metadata)
                .build();

        AnomalyEventProtos.AnomalyEvent event = AnomalyEventProtos.AnomalyEvent.newBuilder()
                .setAccountId("qa-d681ef13-d690-4917-jkhg-6c79b-1")
                .setAnomalyId("AE-2-52-2-T-S-********")
                .addAppId("netbanking_1_DR")
                .addAppId("los_2_DR")
                .addAppId("enet_3_DR")
                .addAppId("microbanking_1_DR")
                .setThresholdType("Static")
                .setOperationType("greater than")
                .setStartTimeGMT(1642065472000L)
                .setEndTimeGMT(1642065472000L)
                .setAnomalyTriggerTimeGMT(1642065472000L)
                .setKpis(kpiInfo)
                .build();


        try {
            ConnectionFactory factory = new ConnectionFactory();
            factory.setHost("**************");
            factory.setPort(5671);
             factory.setVirtualHost("/");
             factory.useSslProtocol();
            Connection connection = factory.newConnection();
            Channel channel = connection.createChannel();

            channel.queueDeclare("txn-collator-test", true, false, false, null);

            ByteArrayOutputStream os = new ByteArrayOutputStream();
            event.writeTo(os);
            channel.basicPublish("", "txn-collator-test", null, os.toByteArray());
            System.out.println("Data Inserted");
            channel.close();

            channel = connection.createChannel();
            channel.basicQos(3);

            try {
                Thread.sleep(5000);
            } catch (Exception e) {
                e.printStackTrace();
            }

            DeliverCallback deliverCallback = (consumerTag, delivery) -> {
                //IssueProtos.Issue message = IssueProtos.Issue.parseFrom(delivery.getBody());
                try {
                    AnomalyEventProtos.AnomalyEvent message = AnomalyEventProtos.AnomalyEvent.parseFrom(new ByteArrayInputStream(delivery.getBody()));
                    System.out.println(" [x] Received '" + message + "'");

                    System.out.println("======================" + message);
                } catch (Exception e) {
                    e.printStackTrace();
                }
                //channel.basicAck(delivery.getEnvelope().getDeliveryTag(), false);
            };
            channel.basicConsume("txn-collator-test", true, deliverCallback, consumerTag -> {
            });


            try {
                Thread.sleep(5000);
            } catch (Exception e) {
                e.printStackTrace();
            }
            channel.close();
            connection.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
