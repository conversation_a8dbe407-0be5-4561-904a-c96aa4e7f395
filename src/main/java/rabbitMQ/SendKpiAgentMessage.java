package rabbitMQ;

import com.appnomic.appsone.common.protbuf.KPIAgentMessageProtos;
import com.rabbitmq.client.AMQP;
import com.rabbitmq.client.Channel;
import com.rabbitmq.client.Connection;
import com.rabbitmq.client.ConnectionFactory;

import java.io.ByteArrayOutputStream;
import java.util.UUID;

public class SendKpiAgentMessage {

    public static void main(String[] args) {
        try {
            ConnectionFactory factory = new ConnectionFactory();
            factory.setHost("127.0.0.1");
            factory.setPort(5672);
            factory.setUsername("guest");
            factory.setPassword("guest");
            factory.useSslProtocol();
            Connection connection = factory.newConnection();
            Channel channel = connection.createChannel();

            channel.queueDeclare("received-component-messages", true, false, false, null);

            KPIAgentMessageProtos.KPIAgentMessage.Builder msg = KPIAgentMessageProtos.KPIAgentMessage.newBuilder()
                    .setAgentUid("e570de02-192-168-13-120")
                    .addInstances(KPIAgentMessageProtos.KPIAgentMessage.Instance
                            .newBuilder()
                            .setInstanceId("192_168_13_120_DATAEMITTER_1")
                            .addKpiData(KPIAgentMessageProtos.KPIAgentMessage.KpiData
                                    .newBuilder()
                                    .setKpiName("ProcessCpuLoad")
                                    .setKpiUid(137)
                                    .setTimeInGMT("2023-01-25 04:28:00")
                                    .setVal("70.0")
                                    .setIsKpiGroup(false)
                                    .setCollectionInterval(60)
                                    .build())
                            .addKpiData(KPIAgentMessageProtos.KPIAgentMessage.KpiData
                                    .newBuilder()
                                    .setKpiName("HeapMemoryUsage-used")
                                    .setKpiUid(137)
                                    .setTimeInGMT("2023-01-25 04:28:00")
                                    .setVal("180208352")
                                    .setIsKpiGroup(false)
                                    .build()))
                    .addInstances(KPIAgentMessageProtos.KPIAgentMessage.Instance
                            .newBuilder()
                            .setInstanceId("redis-node5")
                            .addKpiData(KPIAgentMessageProtos.KPIAgentMessage.KpiData
                                    .newBuilder()
                                    .setKpiName("ProcessCpuLoad")
                                    .setKpiUid(1011)
                                    .setTimeInGMT("2023-01-25 04:28:00")
                                    .setIsKpiGroup(true)
                                    .setKpiGroupName("Component Properties")
                                    .setErrorCode("E1070::Signature verification failed for script \"Fetch_Redis_ConfigWatch.sh\", data collection wont happen for producer \"Fetch_Redis_ConfigWatch\", component instance \"redis-node5\"")
                                    .setKpiType(KPIAgentMessageProtos.KPIAgentMessage.KpiData.KpiType.ConfigWatch)
                                    .setCollectionInterval(43200)
                                    .build())
                            .addKpiData(KPIAgentMessageProtos.KPIAgentMessage.KpiData
                                    .newBuilder()
                                    .setKpiName("REDIS_CLIENT_MAX_OUT_BUFFER")
                                    .setKpiUid(1098)
                                    .setTimeInGMT("2023-01-25 04:28:00")
                                    .setVal("")
                                    .setIsKpiGroup(false)
                                    .setErrorCode("E1070::Signature verification failed for script \"Fetch_Redis_Core_Non_Group.sh\", data collection wont happen for producer \"Fetch_Redis_Core_Non_Group\", component instance \"redis-node5\"")
                                    .setCollectionInterval(60)
                                    .build()))
                    .addInstances(KPIAgentMessageProtos.KPIAgentMessage.Instance
                            .newBuilder()
                            .setInstanceId("192_168_13_120_RHEL")
                            .addKpiData(KPIAgentMessageProtos.KPIAgentMessage.KpiData
                                    .newBuilder()
                                    .setKpiName("FILE_WATCH")
                                    .setKpiUid(265)
                                    .setTimeInGMT("2023-01-25 04:28:00")
                                    .setIsKpiGroup(true)
                                    .setKpiGroupName("File Watcher")
                                    .setKpiType(KPIAgentMessageProtos.KPIAgentMessage.KpiData.KpiType.FileWatch)
                                    .setCollectionInterval(60)
                                    .setWatcherKpiValue(KPIAgentMessageProtos.KPIAgentMessage.KpiData.WatcherKpiValue.newBuilder()
                                            .putKeyValuePair("/etc/crontab", KPIAgentMessageProtos.KPIAgentMessage.KpiData.GroupKpi.newBuilder()
                                                    .putPairs("File Content", "c39252b11aad842fcb75e05c6a27eef8")
                                                    .putPairs("Last Updated Time", "2021-01-08 13:44:10")
                                                    .build())
                                            .build())
                                    .build())
                            .addKpiData(KPIAgentMessageProtos.KPIAgentMessage.KpiData
                                    .newBuilder()
                                    .setKpiName("REDIS_CLIENT_MAX_OUT_BUFFER")
                                    .setKpiUid(1098)
                                    .setTimeInGMT("2023-01-25 04:28:00")
                                    .setVal("")
                                    .setIsKpiGroup(false)
                                    .setErrorCode("E1070::Signature verification failed for script \"Fetch_Redis_Core_Non_Group.sh\", data collection wont happen for producer \"Fetch_Redis_Core_Non_Group\", component instance \"redis-node5\"")
                                    .setCollectionInterval(60)
                                    .build()));

            String corrId = UUID.randomUUID().toString();
            AMQP.BasicProperties persistent = new AMQP.BasicProperties.Builder().deliveryMode(2).correlationId(corrId).build();
            ByteArrayOutputStream os = new ByteArrayOutputStream();
            msg.build().writeDelimitedTo(os);
            channel.basicPublish("", "received-component-messages", persistent, os.toByteArray());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
