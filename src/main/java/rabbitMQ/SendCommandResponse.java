package rabbitMQ;

import com.appnomic.appsone.common.protbuf.CommandResponseProtos;
import com.rabbitmq.client.AMQP;
import com.rabbitmq.client.Channel;
import com.rabbitmq.client.Connection;
import com.rabbitmq.client.ConnectionFactory;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.UUID;
import java.util.concurrent.TimeoutException;

public class SendCommandResponse {
    private final static String QUEUE_NAME = "raw-forensic-messages";

    public static void main(String[] args) throws IOException, TimeoutException {
        ConnectionFactory factory = new ConnectionFactory();
        factory.setHost("127.0.0.1");
        factory.setPort(5672);

        try {
            //factory.useSslProtocol();
            Connection connection = factory.newConnection();
            Channel channel = connection.createChannel();

            channel.queueDeclare(QUEUE_NAME, true, false, false, null);

            System.out.println(System.currentTimeMillis());
            CommandResponseProtos.CommandResponse command = CommandResponseProtos.CommandResponse.newBuilder()
                    .setCommandJobId("*************-4d34-b8f0-cd17f3ee9349-1")
                    .setCommandType("ConfigurationCmds")
                    .setCommand("agent_versions")
                    .setSupervisorIdentifier("7cac4f95-3d19-479a-9813-42a1c3ba0a4b")
                    .setAgentIdentifier("7cac4f95-3d19-479a-9813-42a1c3ba0a4b")
                    .setCmdOut("H4sIAAAAAAAA/6WRXQuCMBSG/4rQ9cY2PWwKQSOMglJxQpehNiqoJtoH/vuGF1EXjSIOnIuXB877cLpro9vboTPtmIwmdmSWqTSJNyrOZC6LNB9S9cQcEMMUE0yZA1mVvceExwijHqUR+BEVb3gSr5eLJB4yDZxsNWGoBgEoCClHVVVxBHXIKx80AtTs++5Ql0dn+ak5Neaszxe5s+trEAEW2N5UiczUPC1c5tYH2aLE90gQBf8ogVNlaXYz097Ldqtdn3jFNoOG+F0D+EeNBw1Y+pk2AgAA")
                    .setTriggerSource("ControlCenter")
                    .putMetadata("AccountId", "qa-d681ef13-d690-4917-jkhg-6c79b-1")
                    .putMetadata("InstanceId", "d681ef13-d690-4917-jkhg-6c79b-9")
                    .putMetadata("CommandId", "agent_versions")
                    .putMetadata("App_Names", "[netbanking_1_DR, los_2_DR, enet_3_DR, microbanking_1_DR]")
                    .setCommandStartTime(System.currentTimeMillis())
                    .setCommandCompleteTime(System.currentTimeMillis())
                    .build();

            String corrId = UUID.randomUUID().toString();
            AMQP.BasicProperties persistent = new AMQP.BasicProperties.Builder().deliveryMode(2).correlationId(corrId).build();
            ByteArrayOutputStream os = new ByteArrayOutputStream();
            command.writeTo(os);
            channel.basicPublish("", QUEUE_NAME, persistent, os.toByteArray());
            System.out.println("Data Inserted ");

            channel.close();
            connection.close();

        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
