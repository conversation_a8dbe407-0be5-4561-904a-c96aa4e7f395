package rabbitMQ;

import com.appnomic.appsone.common.protbuf.AnomalyEventProtos;
import com.rabbitmq.client.Channel;
import com.rabbitmq.client.Connection;
import com.rabbitmq.client.ConnectionFactory;
import com.rabbitmq.client.DeliverCallback;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeoutException;
import java.util.concurrent.atomic.AtomicInteger;

public class AnomalyEventToQueue {

    public static void main(String[] args) throws IOException, TimeoutException {
        Map<String, Double> thresholdMap = new HashMap<>();
        thresholdMap.put("Lower", 0.0);
        thresholdMap.put("Upper", 4.0);
        thresholdMap.put("isInformatic", 0.0);

        Map<String, String> metadata = new HashMap<>();
        metadata.put("KPI_VIOLATION_TIME", "2022-01-13 04:37:00");
        metadata.put("anomalyScore", "0.97");
        metadata.put("attributeName", "ALL");
        metadata.put("isMaintenanceExcluded", "1");
        metadata.put("isInformatic", "0");
        metadata.put("suppression", "1");
        metadata.put("persistence", "1");
        metadata.put("starttime", "1642048620000");
        metadata.put("serviceid", "NB-App-Service-DR");
        metadata.put("thresholdseverity", "Severe");

        /*AnomalyEventProtos.KpiInfo kpiInfo = AnomalyEventProtos.KpiInfo.newBuilder()
                .setKpiAttribute("ALL")
                .setInstanceId("ORACLE_NB_DB_176_Inst_1")
                .setKpiId("1")
                .setValue("0.63")
                .addSvcId("NB-DB-Service")
                .putAllThresholds(thresholdMap)
                .setIsWorkload(false)
                .build();*/

        AnomalyEventProtos.KpiInfo kpiInfo = AnomalyEventProtos.KpiInfo.newBuilder()
                .setKpiAttribute("ALL")
                .setInstanceId("RHEL_NB_App_Host_146_Inst_1-DR")
                .setKpiId("2")
                .setValue("8.04")
                .addSvcId("NB-App-Service-DR")
                .putAllThresholds(thresholdMap)
                .setIsWorkload(false)
                .setThresholdSeverity("Severe")
                .putAllMetadata(metadata)
                .build();

        AnomalyEventProtos.AnomalyEvent event = AnomalyEventProtos.AnomalyEvent.newBuilder()
                .setAccountId("qa-d681ef13-d690-4917-jkhg-6c79b-1")
                .setAnomalyId("AE-2-52-2-T-S-********")
                .addAppId("netbanking_1_DR")
                .addAppId("los_2_DR")
                .addAppId("enet_3_DR")
                .addAppId("microbanking_1_DR")
                .setThresholdType("Static")
                .setOperationType("greater than")
                .setStartTimeGMT(1642065472000L)
                .setEndTimeGMT(1642065472000L)
                .setAnomalyTriggerTimeGMT(1642065472000L)
                .setKpis(kpiInfo)
                .build();

        Connection connection = null;
                Channel channel = null;
        try {
            ConnectionFactory factory = new ConnectionFactory();
            factory.setHost("**************");
            factory.setPort(5671);
            factory.setVirtualHost("/");
            factory.useSslProtocol();
            connection = factory.newConnection();
            channel = connection.createChannel();
        } catch (Exception e) {
            e.printStackTrace();
        }

            /*channel.exchangeDeclare("A1_EVENT_EXCHANGE", BuiltinExchangeType.FANOUT, false);
            channel.queueBind("anomaly-event-action-messages", "A1_EVENT_EXCHANGE", "");
            channel.queueBind("anomaly-event-signal-messages", "A1_EVENT_EXCHANGE", "");*/
            channel.queueDeclare("txn-collator-conventional", true, false, false, Collections.singletonMap("x-queue-type", "stream"));

            ByteArrayOutputStream os = new ByteArrayOutputStream();
            event.writeTo(os);
            channel.basicPublish("", "txn-collator-conventional", null, os.toByteArray());
            System.out.println("Data Inserted");

            AtomicInteger messageCount = new AtomicInteger();
            //channel.basicQos(500000); // QoS must be specified
            DeliverCallback deliverCallback = (consumerTag, delivery) -> {
                //IssueProtos.Issue message = IssueProtos.Issue.parseFrom(delivery.getBody());
                try {
                    AnomalyEventProtos.AnomalyEvent message = AnomalyEventProtos.AnomalyEvent.parseFrom(new ByteArrayInputStream(delivery.getBody()));
                    System.out.println(" [x] Received '" + message + "'");

                    System.out.println("======================" + message);

                    messageCount.getAndIncrement();

                    System.out.println(messageCount.get());
                } catch (Exception e) {
                    e.printStackTrace();
                }
                //channel.basicAck(delivery.getEnvelope().getDeliveryTag(), false);
            };
            channel.basicConsume("txn-collator-conventional", false, Collections.singletonMap("x-stream-offset", messageCount.get()), deliverCallback, consumerTag -> {
            });

            try {
                Thread.sleep(5000);
            } catch (Exception e) {
                e.printStackTrace();
            }
            System.out.println("+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++");

            //  channel = connection.createChannel();
           /* channel.basicPublish("", "txn-collator-conventional", null, os.toByteArray());
            System.out.println("Data Inserted");

        //    channel.basicQos(10000); // QoS must be specified
            channel.basicConsume("txn-collator-conventional", false, Collections.singletonMap("x-stream-offset", messageCount.get()), deliverCallback, consumerTag -> { });
*/
            //channel.close();
            // connection.close();
        /*} finally {
            if(channel != null)
            channel.close();
            if(connection != null)
            connection.close();
        }*/
    }
}
