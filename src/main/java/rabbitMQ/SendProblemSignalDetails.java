package rabbitMQ;

import com.appnomic.appsone.common.protbuf.SignalProtos;
import com.rabbitmq.client.Channel;
import com.rabbitmq.client.Connection;
import com.rabbitmq.client.ConnectionFactory;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.concurrent.TimeoutException;

/**
 * Optimized SignalDetails sender that follows the proto definition exactly
 * and minimizes message size by removing redundant data
 */
public class SendProblemSignalDetails {

    private static Map<String, String> mapOf(String... keyValues) {
        Map<String, String> map = new LinkedHashMap<>();
        for (int i = 0; i < keyValues.length; i += 2) {
            map.put(keyValues[i], keyValues[i + 1]);
        }
        return map;
    }

    private static Map<String, Double> thresholdMap(String... keyValues) {
        Map<String, Double> map = new LinkedHashMap<>();
        for (int i = 0; i < keyValues.length; i += 2) {
            map.put(keyValues[i], Double.parseDouble(keyValues[i + 1]));
        }
        return map;
    }

    public static void main(String[] args) throws IOException, TimeoutException {
        // Minimal metadata
        Map<String, String> signalMetadata = mapOf(
                "account_id", "demo",
                "serviceIdentifier", "heal-api-service"
        );

        // Keep anomalies similar to previous publisher; not strictly required for criteria
        SignalProtos.AnomalyDetail anomaly1 = SignalProtos.AnomalyDetail.newBuilder()
                .setAnomalyId("AE-6-2226058-398-T-S-********")
                .setSeverityId(433)
                .setIsWorkLoad(true)
                .setInstanceId("heal-api-service-instance")
                .setKpiId("398")
                .setKpiGroupId("TransactionMetrics")
                .setCategoryId("Volume")
                .setKpiAttribute("DC")
                .setAnomalyTime(System.currentTimeMillis())
                .addServiceIds("heal-api-service")
                .setKpiValue("297.0")
                .setThresholdType("Static")
                .setOperationType("greater than")
                .putAllThresholds(thresholdMap("severe", "250.0", "warning", "200.0"))
                .putAllMetadata(signalMetadata)
                .build();

        SignalProtos.AnomalyDetail anomaly2 = SignalProtos.AnomalyDetail.newBuilder()
                .setAnomalyId("AE-6-2226058-3766-T-S-29311817")
                .setSeverityId(295)
                .setIsWorkLoad(true)
                .setInstanceId("heal-api-service-instance")
                .setKpiId("3766")
                .setKpiGroupId("TransactionMetrics")
                .setCategoryId("Volume")
                .setKpiAttribute("DC")
                .setAnomalyTime(System.currentTimeMillis())
                .addServiceIds("heal-api-service")
                .setKpiValue("299.0")
                .setThresholdType("Static")
                .setOperationType("greater than")
                .putAllThresholds(thresholdMap("severe", "250.0", "warning", "200.0"))
                .putAllMetadata(signalMetadata)
                .build();


        // Build a PROBLEM signal with SAME incidentId but ADDITIONAL services compared to cached profile
        // Previously cached likely had only "heal-api-service"; here we add "heal-db-service" and "auth-service"
        String uniqueSvc = "svc-demo-" + System.currentTimeMillis();
        SignalProtos.SignalDetails signalDetails = SignalProtos.SignalDetails.newBuilder()
                .setAccountId("demo")
                .setSignalType("PROBLEM")
                .setSignalId("P-6-398-7021-**********") // keep same to hit cached path
                .setSignalStatus("OPEN")
                .setIsRemainder(false)
                .setIsSeverityChanged(false)
                .setIsServiceAdded(false)
                .putAllMetadata(signalMetadata)
                .addAnomalyDetails(anomaly1)
                .addAnomalyDetails(anomaly2)
                // Related signals (can be left as-is)
                .addRelatedSignals("E-6-397-7021-**********")
                .addRelatedSignals("E-6-406-7021-**********")
//                .addRelatedSignals("E-6-397-7021-**********")
//                .addRelatedSignals("E-6-3766-7021-**********")
//                .addRelatedSignals("E-6-398-7021-**********")
//                .addRelatedSignals("E-6-406-7021-**********")
                // Services: include old ones plus NEW ones to trigger updateServices()
                .addServiceIds("heal-api-service")
                // .addServiceIds("heal-db-service")
                .addServiceIds(uniqueSvc)
                .addRootCauseServiceIds("heal-api-service")
                .addRootCauseServiceIds("heal-db-service")
                .addRootCauseServiceIds(uniqueSvc)
                .addEntryServiceIds("heal-api-service")
                .addEntryServiceIds("auth-service")
                .setSignalSeverityId(295)
                .setStartTime(System.currentTimeMillis() - 60_000)
                .setUpdateTime(System.currentTimeMillis())
                .build();

        // Print quick summary
        System.out.println("=== Criteria-Match PROBLEM SignalDetails ===");
        System.out.println("- Signal ID: " + signalDetails.getSignalId());
        System.out.println("- Services count: " + signalDetails.getServiceIdsCount());
        System.out.println("- ServiceIds: " + signalDetails.getServiceIdsList());

        // Send to RabbitMQ
        ConnectionFactory factory = new ConnectionFactory();
       // factory.setHost("**************");
            factory.setHost("**************");
        factory.setPort(5672);

        try (Connection connection = factory.newConnection();
             Channel channel = connection.createChannel()) {

            channel.queueDeclare("signal-messages", true, false, false, null);

            ByteArrayOutputStream os = new ByteArrayOutputStream();
            signalDetails.writeTo(os);

            channel.basicPublish("", "signal-messages", null, os.toByteArray());
            System.out.println("Criteria-match PROBLEM SignalDetails published to queue successfully!");
        }
    }
}
