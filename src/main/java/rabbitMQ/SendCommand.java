package rabbitMQ;

import com.appnomic.appsone.common.protbuf.CommandRequestProtos;
import com.rabbitmq.client.Channel;
import com.rabbitmq.client.Connection;
import com.rabbitmq.client.ConnectionFactory;

import java.io.ByteArrayOutputStream;

public class SendCommand {
    private final static String QUEUE_NAME = "command-messages";

    public static void main(String[] args) {
        ConnectionFactory factory = new ConnectionFactory();
        //factory.setHost("**************");
        factory.setHost("**************");
        //factory.setHost("127.0.0.1");
        factory.setPort(5671);

        try {
            factory.setVirtualHost("/");
            factory.useSslProtocol();
            Connection connection = factory.newConnection();
            Channel channel = connection.createChannel();

            channel.queueDeclare(QUEUE_NAME, true, false, false, null);

            //String password = Commons.encrypt("vittech_321");

            /*CommandRequestProtos.Command command = CommandRequestProtos.Command.newBuilder()
                    .setCommandJobId("ef864d76-b2ad-3d03-8db5-de491962465b")
                    .setCommandType("Execute")
                    .setCommand("fetch_network_util")
                    .setCommandOutputType("Blob")
                    .setCommandExecType("Execute")
                    .putArguments("interval", "5")
                    .putArguments("sample", "5")

                    *//*.putArguments("db_name", "orcl24")
                    .putArguments("connect_port", "1521")
                    .putArguments("db_user", "c##scott")
                    .putArguments("kpi_category", "DBExecutions")
                    .putArguments("ipaddr", "*************")
                    .putArguments("passcode", "Q4GQJcLHgWk=")*//*
                    .putArguments("HostAddress", "**************")
                    .putArguments("HostPassword", "FEhs+/Jij9PDEY2cwa3fyQ==")
                    .putArguments("HostUsername", "appsone")
                    .putArguments("SshPort", "22")
                    .setCommandTimeout(180)
                    .setSupervisorCtrlTTL(300)
                    .setRetryNumber(3)
                    .build();

            CommandRequestProtos.CommandRequest commandRequest = CommandRequestProtos.CommandRequest.newBuilder()
                    .addCommands(command)
                    .addSupervisorIdentifiers("265b168f-f7c6-44f1-8d8b-546549d3a802")
                    .setAgentType("ForensicAgent")
                    .setAgentIdentifier("e570de02-c585-4917-bbb7-5c97b35e-5")
                    .setTriggerSource("ForensicPipeline")
                    .setUserDetailsID("7640123a-fbde-4fe5-9812-581cd1e3a9c1")
                    .setTriggerTime(System.currentTimeMillis())
                    .setViolationTime(System.currentTimeMillis())
                    .putMetadata("AccountId", "d681ef13-d690-4917-jkhg-6c79b-1")
                    .putMetadata("InstanceId", "ORACLE_NB_DB_176_Inst_1")
                    .putMetadata("CategoryId", "DBExecutions")
                    .putMetadata("ForensicId", "FId-1")
                    .putMetadata("ForensicEventTime", System.currentTimeMillis() + "")
                    .putMetadata("IsWorkLoad", "Yes")
                    .putMetadata("KPIId", "1")
                    .putMetadata("KPIAttribute", "0")
                    .putMetadata("SupervisorMode", "REMOTE")
                    .putMetadata("ProducerType", "JDBC")
                    .build();*/

            CommandRequestProtos.Command command = CommandRequestProtos.Command.newBuilder()
                    .setCommandJobId("0cef1a75-f8f0-426e-9f15-56d0fc36f1162132")
                    .setCommandType("ForensicCmds")
                    .setCommandOutputType("Blob")
                    .setCommandExecType("LongPolling")
                    .setCommand("fetch_cpu_util")
                    .putArguments("record_count", "10")
                    .putArguments("trace_duration", "5")
                    //.setCommand("DBWrapper")
                    /*.putArguments("CategoryName", "")
                    .putArguments("Username", "5")
                    .putArguments("Password", "5")
                    .putArguments("HostAddress", "5")
                    .putArguments("MonitorPort", "5")
                    .putArguments("ConnectWithValue", "5")*/
                    /*.putArguments("kpi_category", "DBExecutions")
                    .putArguments("db_name", "orcl24")
                    .putArguments("connect_port", "1521")
                    .putArguments("db_user", "c##scott")
                    .putArguments("ipaddr", "*************")
                    .putArguments("passcode", "Q4GQJcLHgWk=")*/
                    .putArguments("HostAddress", "*************")
                    .putArguments("HostPassword", "FEhs+/Jij9PDEY2cwa3fyQ==")
                    .putArguments("HostUsername", "appsone")
                    .putArguments("SshPort", "22")
                    .setCommandTimeout(180)
                    .setSupervisorCtrlTTL(300)
                    .setRetryNumber(3)
                    .build();
            CommandRequestProtos.CommandRequest commandRequest = CommandRequestProtos.CommandRequest.newBuilder()
                    .addCommands(command)
                    //.addSupervisorIdentifiers("07912fa4-4fbf-43bd-9bfd-11f9372a616b")
                    .addSupervisorIdentifiers("SupervisorControllerIdentifier")
                    .setAgentType("ForensicAgent")
                    .setAgentIdentifier("e570de02-c585-4917-bbb7-5c97b35e-14")
                    .setTriggerSource("ActionProcessor")
                    .setUserDetailsID("7640123a-fbde-4fe5-9812-581cd1e3a9c1")
                    .setTriggerTime(System.currentTimeMillis())
                    .setViolationTime(System.currentTimeMillis())
                    .putMetadata("AccountId", "d681ef13-d690-4917-jkhg-6c79b-1")
                    .putMetadata("InstanceId", "SOLARIS_LOS_HOST_110_Inst_1")
                    .putMetadata("CategoryId", "CPU")
                    .putMetadata("ActionId", "action-oob_cpu")
                    .putMetadata("ActionTriggerTime", System.currentTimeMillis() + "")
                    .putMetadata("IsWorkLoad", "false")
                    .putMetadata("KPIId", "1") //1, 180
                    .putMetadata("KPIAttribute", "22")
                    .putMetadata("SupervisorMode", "REMOTE")
                    .putMetadata("ProducerType", "SCRIPT") //JDBC or SCRIPT
                    .build();

                    ByteArrayOutputStream os = new ByteArrayOutputStream();
                    commandRequest.writeTo(os);
                    channel.basicPublish("", QUEUE_NAME, null, os.toByteArray());
                    System.out.println("Data Inserted");
            channel.close();
            connection.close();
        }  catch (Exception e) {
            e.printStackTrace();
        }
    }
}
