package rabbitMQ;

import com.appnomic.appsone.common.protbuf.SignalProtos;
import com.appnomic.appsone.common.protbuf.SignalProtos.AnomalyDetail;
import com.appnomic.appsone.common.protbuf.SignalProtos.ServiceSummary;
import com.rabbitmq.client.Channel;
import com.rabbitmq.client.Connection;
import com.rabbitmq.client.ConnectionFactory;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Arrays;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeoutException;

/**
 * Multiple variants of SignalDetails messages for different scenarios
 */
public class SendSignalDetailsVariants {
    
    private static Map<String, String> mapOf(String... keyValues) {
        Map<String, String> map = new LinkedHashMap<>();
        for (int i = 0; i < keyValues.length; i += 2) {
            map.put(keyValues[i], keyValues[i + 1]);
        }
        return map;
    }
    
    private static Map<String, Double> thresholdMap(String... keyValues) {
        Map<String, Double> map = new LinkedHashMap<>();
        for (int i = 0; i < keyValues.length; i += 2) {
            map.put(keyValues[i], Double.parseDouble(keyValues[i + 1]));
        }
        return map;
    }

    public static void main(String[] args) throws IOException, TimeoutException {
        
        ConnectionFactory factory = new ConnectionFactory();
        factory.setHost("**************");
        factory.setPort(5672);
        
        try (Connection connection = factory.newConnection();
             Channel channel = connection.createChannel()) {
            
            channel.queueDeclare("signal-messages-aiops", true, false, false, null);
            
            // Send different types of signals
            sendProblemSignal(channel);
            sendEarlyWarningSignal(channel);
            sendInfoSignal(channel);
            sendBatchSignal(channel);
            
            System.out.println("All SignalDetails variants published successfully!");
        }
    }
    
    /**
     * PROBLEM type signal - Critical issues
     */
    private static void sendProblemSignal(Channel channel) throws IOException {
        AnomalyDetail criticalAnomaly = AnomalyDetail.newBuilder()
                .setAnomalyId("AE-CRITICAL-001")
                .setSeverityId(500) // Critical severity
                .setIsWorkLoad(true)
                .setInstanceId("prod-db-01")
                .setKpiId("DatabaseConnections")
                .setKpiGroupId("DatabaseMetrics")
                .setCategoryId("DatabasePerformance")
                .setKpiAttribute("ConnectionPool")
                .setAnomalyTime(System.currentTimeMillis())
                .addServiceIds("DatabaseService")
                .addServiceIds("AuthService")
                .setKpiValue("95.0")
                .setThresholdType("Dynamic")
                .setOperationType("greater than")
                .putAllThresholds(thresholdMap(
                    "critical", "90.0",
                    "warning", "80.0"
                ))
                .putAllMetadata(mapOf(
                    "anomalyScore", "0.95",
                    "eventStatus", "Critical",
                    "eventType", "threshold_breach",
                    "impactLevel", "HIGH"
                ))
                .build();

        ServiceSummary dbServiceSummary = ServiceSummary.newBuilder()
                .setServiceId("DatabaseService")
                .addSevereAnomalies("AE-CRITICAL-001")
                .addInstanceIds("prod-db-01")
                .putCategories("DatabaseConnections", "DatabasePerformance")
                .setAnomalyTime(System.currentTimeMillis())
                .build();

        SignalProtos.SignalDetails problemSignal = SignalProtos.SignalDetails.newBuilder()
                .setAccountId("prod-account")
                .setSignalType("PROBLEM")
                .setSignalId("PROB-" + System.currentTimeMillis())
                .setSignalStatus("CRITICAL")
                .setIsRemainder(false)
                .setIsSeverityChanged(true)
                .setIsServiceAdded(false)
                .putAllMetadata(mapOf(
                    "priority", "P1",
                    "category", "Database",
                    "environment", "Production"
                ))
                .addAnomalyDetails(criticalAnomaly)
                .setStartTime(System.currentTimeMillis())
                .setUpdateTime(System.currentTimeMillis())
                .addServiceIds("DatabaseService")
                .addServiceIds("AuthService")
                .addRootCauseServiceIds("DatabaseService")
                .setSignalSeverityId(500)
                .setAnomalies(1)
                .addServiceSummary(dbServiceSummary)
                .build();

        publishMessage(channel, problemSignal, "PROBLEM");
    }
    
    /**
     * EARLY_WARNING type signal - Predictive alerts
     */
    private static void sendEarlyWarningSignal(Channel channel) throws IOException {
        AnomalyDetail warningAnomaly = AnomalyDetail.newBuilder()
                .setAnomalyId("AE-WARNING-002")
                .setSeverityId(300)
                .setIsWorkLoad(false)
                .setInstanceId("app-server-02")
                .setKpiId("MemoryUsage")
                .setKpiGroupId("SystemMetrics")
                .setCategoryId("ApplicationPerformance")
                .setKpiAttribute("HeapMemory")
                .setAnomalyTime(System.currentTimeMillis())
                .addServiceIds("WebService")
                .setKpiValue("75.0")
                .setThresholdType("ML_Based")
                .setOperationType("trending_up")
                .putAllThresholds(thresholdMap(
                    "predicted_breach", "85.0",
                    "current_trend", "75.0"
                ))
                .putAllMetadata(mapOf(
                    "anomalyScore", "0.65",
                    "eventStatus", "Warning",
                    "eventType", "trend_analysis",
                    "predictionConfidence", "0.82"
                ))
                .build();

        SignalProtos.SignalDetails warningSignal = SignalProtos.SignalDetails.newBuilder()
                .setAccountId("prod-account")
                .setSignalType("EARLY_WARNING")
                .setSignalId("EW-" + System.currentTimeMillis())
                .setSignalStatus("OPEN")
                .setIsRemainder(false)
                .setIsSeverityChanged(false)
                .setIsServiceAdded(false)
                .putAllMetadata(mapOf(
                    "priority", "P3",
                    "category", "Performance",
                    "predictionWindow", "30min"
                ))
                .addAnomalyDetails(warningAnomaly)
                .setStartTime(System.currentTimeMillis())
                .setUpdateTime(System.currentTimeMillis())
                .addServiceIds("WebService")
                .setSignalSeverityId(300)
                .setAnomalies(1)
                .build();

        publishMessage(channel, warningSignal, "EARLY_WARNING");
    }
    
    /**
     * INFO type signal - Informational updates
     */
    private static void sendInfoSignal(Channel channel) throws IOException {
        SignalProtos.SignalDetails infoSignal = SignalProtos.SignalDetails.newBuilder()
                .setAccountId("prod-account")
                .setSignalType("INFO")
                .setSignalId("INFO-" + System.currentTimeMillis())
                .setSignalStatus("RESOLVED")
                .setIsRemainder(false)
                .setIsSeverityChanged(false)
                .setIsServiceAdded(false)
                .putAllMetadata(mapOf(
                    "category", "Maintenance",
                    "description", "Scheduled maintenance completed",
                    "duration", "30min"
                ))
                .setStartTime(System.currentTimeMillis() - 1800000) // 30 min ago
                .setUpdateTime(System.currentTimeMillis())
                .addServiceIds("MaintenanceService")
                .setSignalSeverityId(100)
                .setAnomalies(0)
                .build();

        publishMessage(channel, infoSignal, "INFO");
    }
    
    /**
     * BATCH type signal - Batch processing results
     */
    private static void sendBatchSignal(Channel channel) throws IOException {
        // Multiple anomalies for batch processing
        List<AnomalyDetail> batchAnomalies = Arrays.asList(
            createBatchAnomaly("BATCH-001", "BatchProcessor-1", "ProcessingTime", "120.0"),
            createBatchAnomaly("BATCH-002", "BatchProcessor-2", "QueueLength", "500.0"),
            createBatchAnomaly("BATCH-003", "BatchProcessor-3", "ErrorRate", "5.0")
        );

        SignalProtos.SignalDetails.Builder batchSignalBuilder = SignalProtos.SignalDetails.newBuilder()
                .setAccountId("batch-account")
                .setSignalType("BATCH")
                .setSignalId("BATCH-" + System.currentTimeMillis())
                .setSignalStatus("PROCESSING")
                .setIsRemainder(false)
                .setIsSeverityChanged(true)
                .setIsServiceAdded(true)
                .putAllMetadata(mapOf(
                    "batchId", "BATCH-JOB-" + System.currentTimeMillis(),
                    "category", "BatchProcessing",
                    "totalJobs", "3"
                ))
                .setStartTime(System.currentTimeMillis())
                .setUpdateTime(System.currentTimeMillis())
                .addServiceIds("BatchProcessor-1")
                .addServiceIds("BatchProcessor-2")
                .addServiceIds("BatchProcessor-3")
                .addRootCauseServiceIds("BatchProcessor-2") // Queue length is root cause
                .setSignalSeverityId(400)
                .setAnomalies(3);

        // Add all batch anomalies
        for (AnomalyDetail anomaly : batchAnomalies) {
            batchSignalBuilder.addAnomalyDetails(anomaly);
        }

        SignalProtos.SignalDetails batchSignal = batchSignalBuilder.build();
        publishMessage(channel, batchSignal, "BATCH");
    }
    
    private static AnomalyDetail createBatchAnomaly(String anomalyId, String serviceId, String kpiId, String value) {
        return AnomalyDetail.newBuilder()
                .setAnomalyId(anomalyId)
                .setSeverityId(400)
                .setIsWorkLoad(true)
                .setInstanceId(serviceId + "-instance")
                .setKpiId(kpiId)
                .setKpiGroupId("BatchMetrics")
                .setCategoryId("BatchProcessing")
                .setKpiAttribute("ALL")
                .setAnomalyTime(System.currentTimeMillis())
                .addServiceIds(serviceId)
                .setKpiValue(value)
                .setThresholdType("Static")
                .setOperationType("greater than")
                .putAllThresholds(thresholdMap("threshold", "100.0"))
                .putAllMetadata(mapOf(
                    "anomalyScore", "0.75",
                    "eventStatus", "Active",
                    "eventType", "batch_processing"
                ))
                .build();
    }
    
    private static void publishMessage(Channel channel, SignalProtos.SignalDetails signal, String type) throws IOException {
        ByteArrayOutputStream os = new ByteArrayOutputStream();
        signal.writeTo(os);
        byte[] messageData = os.toByteArray();

        channel.basicPublish("", "signal-messages-aiops", null, messageData);

        // Print detailed size information
        System.out.println("=== " + type + " Signal Published ===");
        System.out.println("- Signal ID: " + signal.getSignalId());
        System.out.println("- Serialized size: " + signal.getSerializedSize() + " bytes");
        System.out.println("- Actual message size: " + messageData.length + " bytes");
    //    System.out.println("- Number of anomalies: " + signal.getAnomaliesCount());
        System.out.println("- Number of services: " + signal.getServiceIdsCount());
        System.out.println("- Metadata entries: " + signal.getMetadataCount());
        System.out.println();
    }
}
