package rabbitMQ;

import com.appnomic.appsone.common.protbuf.IssueProtos;
import com.rabbitmq.client.Channel;
import com.rabbitmq.client.Connection;
import com.rabbitmq.client.ConnectionFactory;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeoutException;

public class SendIssueToQueue {

    public static void main(String[] args) throws IOException, TimeoutException {

        ConnectionFactory factory = new ConnectionFactory();
        factory.setHost("127.0.0.1");
        factory.setPort(5672);

        try (Connection connection = factory.newConnection()) {
            try (Channel channel = connection.createChannel()) {
                channel.queueDeclare("issue-messages", true, false, false, null);
                String message = "Hello World!";
           /* List<IssueProtos.ServiceDetails> serviceDetails = new ArrayList<>();
            IssueProtos.ServiceDetails serviceDetail = IssueProtos.ServiceDetails.newBuilder().setServiceId("Booking").setImpactedType("RCA").build();
            serviceDetails.add(serviceDetail);

            serviceDetail = IssueProtos.ServiceDetails.newBuilder().setServiceId("Hotels-Web").setImpactedType("ENTRY-LEVEL").build();
            serviceDetails.add(serviceDetail);

            IssueProtos.Issue issue = IssueProtos.Issue.newBuilder()
                    .setAccountId("8b9d77dd-64dc-4945-856d-b294b89c2cae")
                    .setSourceType("PROBLEM")
                    .setProblemId("Problem-ID16")
                    .setStartTime(System.currentTimeMillis())
                    .setEndTime(System.currentTimeMillis()+120000)
                    .addAllServiceDetails(serviceDetails)
                    .build();*//*
            *//*List<IssueProtos.ServiceDetails> serviceDetails = new ArrayList<>();
            IssueProtos.ServiceDetails serviceDetail = IssueProtos.ServiceDetails.newBuilder().setServiceId("Oracle-Service-3").setImpactedType("RCA").build();
            serviceDetails.add(serviceDetail);
            serviceDetail = IssueProtos.ServiceDetails.newBuilder().setServiceId("JIMTransactions-3").setImpactedType("ENTRY-LEVEL").build();
            serviceDetails.add(serviceDetail);
            IssueProtos.Issue issue = IssueProtos.Issue.newBuilder()
                    .setAccountId("ce582939-b3b9-40b8-8c46-18de487084fc")
                    //.setAccountId("1")
                    .setSourceType("PROBLEM")
                    //.setProblemId("Problem-1")
                    .setIssueId("Problem-2")
                    .setStartTime(System.currentTimeMillis())
                    .setEndTime(System.currentTimeMillis()+60000)
                    .addAllServiceDetails(serviceDetails)
                    .build();*/


                List<IssueProtos.ServiceDetails> serviceDetails = new ArrayList<>();
                IssueProtos.ServiceDetails serviceDetail = IssueProtos.ServiceDetails.newBuilder().setServiceId("WebLogic-Service").setImpactedType("ENTRY-LEVEL").build();
                serviceDetails.add(serviceDetail);
                serviceDetail = IssueProtos.ServiceDetails.newBuilder().setServiceId("Booking1").build();
                serviceDetails.add(serviceDetail);

                IssueProtos.Issue issue = IssueProtos.Issue.newBuilder()
                        .setAccountId("d681ef13-d690-4917-jkhg-6c79b-1")
                        .setSourceType("ANOMALY")
                        .setIssueId("Anomaly-1")
                        .setStartTime(System.currentTimeMillis())
                        .setEndTime(System.currentTimeMillis() + 60000)
                        .addAllServiceDetails(serviceDetails)
                        .putPlaceHolders("IssueID", "Anomaly-1")
                        .putPlaceHolders("IssueStatus", "OPEN")
                        .putPlaceHolders("SourceType", "ANOMALY")
                        .putPlaceHolders("StartTime", Long.toString(System.currentTimeMillis()))
                        .putPlaceHolders("EndTime", Long.toString(System.currentTimeMillis() + 10000))
                        .putPlaceHolders("AccountIdentifier", "d681ef13-d690-4917-jkhg-6c79b-1")
                        .putPlaceHolders("A1Port", "dsProps.getProperty(\"ds.server.port\")")
                        .putPlaceHolders("A1Protocol", "dsProps.getProperty(\"ds.server.protocol\")")
                        .putPlaceHolders("A1HostAddress", "dsProps.getProperty(\"ds.server.ip\")")
                        .putPlaceHolders("AccountName", "d681ef13-d690-4917-jkhg-6c79b-1-NAME")
                        .putPlaceHolders("ApplicationNames", String.join(",", "app1, app2"))
                        .putPlaceHolders("ServiceName", String.join(",", "Webservice Logic"))
                        .putPlaceHolders("KpiName", "getName()")
                        .putPlaceHolders("KpiGroupName", "getGroupName()")
                        .putPlaceHolders("KpiUnits", "kpiUnits")
                        .putPlaceHolders("OPERATION_TYPE", "getOperationType()")
                        .putPlaceHolders("ThresholdType", "getThresholdType()")
                        .putPlaceHolders("IssueTime", String.valueOf(System.currentTimeMillis()))
                        .putPlaceHolders("ViolatedValue", "getValue()")
                        .putPlaceHolders("MinMaxThresholds", String.valueOf("getThresholdsMap()"))
                        .putPlaceHolders("ServicePrimaryKey", "serviceId")
                        .putPlaceHolders("GroupValue", "Variables:" + "service.kpi.attribute")
                        .putPlaceHolders("InstanceName", "instanceName")
                        .putPlaceHolders("KpiType", "kpi type")
                        .build();

                channel.basicPublish("", "issue-messages", null, issue.toByteArray());
                System.out.println(" [x] Sent '" + message + "'");
            }
        }
    }
}
