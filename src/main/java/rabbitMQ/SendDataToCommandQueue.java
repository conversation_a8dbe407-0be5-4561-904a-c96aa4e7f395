package rabbitMQ;

import com.appnomic.appsone.common.protbuf.CommandRequestProtos;
import com.rabbitmq.client.Channel;
import com.rabbitmq.client.Connection;
import com.rabbitmq.client.ConnectionFactory;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.concurrent.TimeoutException;

public class SendDataToCommandQueue {
    private final static String QUEUE_NAME = "command-messages";

    public static void main(String[] args) throws IOException, TimeoutException {
        ConnectionFactory factory = new ConnectionFactory();
        factory.setHost("127.0.0.1");
        factory.setPort(5672);

       /* try {
            factory.useSslProtocol();
        } catch (NoSuchAlgorithmException | KeyManagementException e) {
            e.printStackTrace();
        }*/

        Connection connection = factory.newConnection();
        Channel channel = connection.createChannel();
        ByteArrayOutputStream os = new ByteArrayOutputStream();

        try {
            channel.queueDeclare(QUEUE_NAME, true, false, false, null);

            CommandRequestProtos.Command command = CommandRequestProtos.Command.newBuilder()
                    .setCommandJobId(" ")
                    .setCommandType("Execute")
                    .setCommand("transaction, transaction1")
                    .setCommandOutputType("Blob")
                    .setCommandExecType("Execute")
                    .putArguments("AgentMode", "Verbose") //Auto/SwitchOff
                    .putArguments("SnapshotCollectionDuration", "15")
                    .putArguments("SnapshotsPerMin", "10")
                    .putArguments("SilentWindow", "5")
                    .putEnvArgs("SnapshotsCollectionForFailures", "true")
                    .setCommandTimeout(180)
                    .setSupervisorCtrlTTL(300)
                    .setRetryNumber(3)
                    .build();

            CommandRequestProtos.CommandRequest cmdDet = CommandRequestProtos.CommandRequest.newBuilder()
                    .addCommands(command)
                    .addSupervisorIdentifiers("")
                    .setAgentType(" ")
                    .setAgentIdentifier(" ")
                    .setTriggerSource("ControlCenter")
                    .setUserDetailsID(" ")
                    .setTriggerTime(System.currentTimeMillis())
                    .setViolationTime(System.currentTimeMillis())
                    .putMetadata("InstanceId", " ")
                    .build();

            cmdDet.writeDelimitedTo(os);

            channel.basicPublish("", QUEUE_NAME, null, os.toByteArray());

            System.out.println("Data Inserted");
            System.out.println(cmdDet);
        } finally {
            os.close();
            channel.close();
            connection.close();
        }
    }
}
