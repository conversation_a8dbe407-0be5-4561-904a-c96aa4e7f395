package rabbitMQ;

import com.appnomic.appsone.common.protbuf.KPIAgentMessageProtos;
import com.rabbitmq.client.AMQP;
import com.rabbitmq.client.Channel;
import com.rabbitmq.client.Connection;
import com.rabbitmq.client.ConnectionFactory;

import java.io.ByteArrayOutputStream;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.HashMap;
import java.util.Map;
import java.util.TimeZone;


public class CoreKPI
{
    private final static String KPI_QUEUE_NAME = "component-messages";
    private final static AMQP.BasicProperties persistent = new AMQP.BasicProperties.Builder().deliveryMode(2).build();


    public static void main(String[] argv) throws Exception {

        ConnectionFactory factory = new ConnectionFactory();
        factory.useSslProtocol();
        // factory.setHost("**************");
        factory.setHost("127.0.0.1");
        //  factory.setHost("**************");
        //factory.setVirtualHost("/");
        factory.setPort(5672);
        Connection connection = factory.newConnection();
        Channel channel = connection.createChannel();
        channel.queueDeclare(KPI_QUEUE_NAME, true, false, false, null);


        Calendar c = Calendar.getInstance();
        // while (true) {
        int min = 5;
        int max = 20;
        int minMem = 5;
        int maxMem = 25;

        int kpiVal = 0;
        int usedDev = 0;
        int usedExport = 0;
        int usedRoot = 0;
        int availDev = 0;
        int availExport = 0;
        int availRoot = 0;

        for (int i = 0; i < 100; i++) {
            try {
                DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:00");
                dateFormat.setTimeZone(TimeZone.getTimeZone("GMT"));
                Calendar cal = Calendar.getInstance();
                //cal.setTimeInMillis(1559155800000l);
                String StartTimeInGMT = dateFormat.format(cal.getTime());



                kpiVal = (int) ((Math.random() * ((max - min) + 1)) + min);
                usedDev = (int) ((Math.random() * ((maxMem - minMem) + 1)) + minMem);
                usedExport = (int) ((Math.random() * ((maxMem - minMem) + 1)) + minMem);
                usedRoot = (int) ((Math.random() * ((maxMem - minMem) + 1)) + minMem);

                Map<String, String> established = new HashMap<>();
                //  established.put("sshd", "0");
                established.put("192.168.13.154,8080", "0");
                // established.put("*,8081", "3"); //0 means unavailable
                //established.put("*,8082", "4"); //0 means unavailable


                KPIAgentMessageProtos.KPIAgentMessage.KpiData.GroupKpi.Builder establishedConn = KPIAgentMessageProtos.KPIAgentMessage.KpiData.GroupKpi.newBuilder()
                        .putAllPairs(established);

                Map<String, String> CloseWait = new HashMap<>();
                CloseWait.put("*,7777", String.valueOf((int) (Math.random() * ((max - min) + 1)) + min));
                CloseWait.put("*,8081", String.valueOf((int) (Math.random() * ((max - min) + 1)) + min));
                CloseWait.put("*,8082", String.valueOf((int) (Math.random() * ((max - min) + 1)) + min));


                KPIAgentMessageProtos.KPIAgentMessage.KpiData.GroupKpi.Builder closeWaitConn = KPIAgentMessageProtos.KPIAgentMessage.KpiData.GroupKpi.newBuilder()
                        .putAllPairs(CloseWait);

                Map<String, String> used = new HashMap<>();
                used.put("/dev", String.valueOf(usedDev));
                used.put("/export", String.valueOf(usedExport));
                used.put("/", String.valueOf(usedRoot));

                KPIAgentMessageProtos.KPIAgentMessage.KpiData.GroupKpi.Builder UsedSizeMb = KPIAgentMessageProtos.KPIAgentMessage.KpiData.GroupKpi.newBuilder()
                        .putAllPairs(used);

                availDev = (int) ((Math.random() * ((maxMem - minMem) + 1)) + minMem);
                availExport = (int) ((Math.random() * ((maxMem - minMem) + 1)) + minMem);
                availRoot = (int) ((Math.random() * ((maxMem - minMem) + 1)) + minMem);

              /*  Map<String, String> available = new HashMap<>();
                available.put("/dev", String.valueOf(availDev));
                available.put("/export", String.valueOf(availExport));
                available.put("/", String.valueOf(availRoot));

                KPIAgentMessageProtos.KPIAgentMessage.KpiData.GroupKpi.Builder AvailSizeMb = KPIAgentMessageProtos.KPIAgentMessage.KpiData.GroupKpi.newBuilder()
                        .putAllPairs(available);

                Map<String, String> total = new HashMap<>();
                total.put("/dev", String.valueOf(availDev + usedDev));
                total.put("/export", String.valueOf(availExport + usedExport));
                total.put("/", String.valueOf(availRoot + usedRoot));

                KPIAgentMessageProtos.KPIAgentMessage.KpiData.GroupKpi.Builder TotalSizeMb = KPIAgentMessageProtos.KPIAgentMessage.KpiData.GroupKpi.newBuilder()
                        .putAllPairs(total); */

                KPIAgentMessageProtos.KPIAgentMessage msg1 = KPIAgentMessageProtos.KPIAgentMessage.newBuilder()
                        .setAgentUid("e570de02-c585-4917-bbb7-5c97b35e-2")
                        //  .setAgentUid("e570de02-c585-4917-bbb7-5c97b35e-4")
                        .addInstances(KPIAgentMessageProtos.KPIAgentMessage.Instance
                                .newBuilder()
                                .setInstanceId("RHEL_NB_Finacle_Host_204_Inst_1")
                                //   .setInstanceId("AIX_192.168.13.124_Inst_1")
                                //      .setInstanceId("RHEL_NB_Web_Host_154_Inst_1")

                                .addKpiData(KPIAgentMessageProtos.KPIAgentMessage.KpiData
                                        .newBuilder()
                                        .setKpiName("CPU_UTIL")
                                        .setVal(String.valueOf(10))
                                        // .setKpiName("Process Running")
                                        .setKpiUid(1)
                                        // .setKpiUid(251)
                                        .setTimeInGMT(StartTimeInGMT)
                                        .setKpiType(KPIAgentMessageProtos.KPIAgentMessage.KpiData.KpiType.Core)
                                        .setIsKpiGroup(false)
                                        .setCollectionInterval(60)
                                        .build())

                                .build())
                        .build();


                KPIAgentMessageProtos.KPIAgentMessage msg2 = KPIAgentMessageProtos.KPIAgentMessage.newBuilder()
                        .setAgentUid("e570de02-c585-4917-bbb7-5c97b35e-1")
                        //  .setAgentUid("e570de02-c585-4917-bbb7-5c97b35e-4")
                        .addInstances(KPIAgentMessageProtos.KPIAgentMessage.Instance
                                .newBuilder()
                                .setInstanceId("RHEL_NB_Finacle_Host_205_Inst_1")
                                //   .setInstanceId("AIX_192.168.13.124_Inst_1")
                                //      .setInstanceId("RHEL_NB_Web_Host_154_Inst_1")

                                .addKpiData(KPIAgentMessageProtos.KPIAgentMessage.KpiData
                                        .newBuilder()
                                        .setKpiName("CPU_UTIL")
                                        .setVal(String.valueOf(20))
                                        // .setKpiName("Process Running")
                                        .setKpiUid(1)
                                        // .setKpiUid(251)
                                        .setTimeInGMT(StartTimeInGMT)
                                        .setKpiType(KPIAgentMessageProtos.KPIAgentMessage.KpiData.KpiType.Core)
                                        .setIsKpiGroup(false)
                                        .setCollectionInterval(60)
                                        .build())

                                .build())
                        .build();

                Channel finalChannel = channel;

                try {
                    ByteArrayOutputStream os = new ByteArrayOutputStream();
                    ByteArrayOutputStream os1 = new ByteArrayOutputStream();

                    msg1.writeDelimitedTo(os);
                    msg2.writeDelimitedTo(os1);
                    System.out.println(msg1);
                    System.out.println(msg2);


                    finalChannel.basicPublish("", KPI_QUEUE_NAME, persistent, os.toByteArray());
                    finalChannel.basicPublish("", KPI_QUEUE_NAME, persistent, os1.toByteArray());
                } catch (Exception e) {
                    e.printStackTrace();
                }

                Thread.sleep(58000);
                cal.add(Calendar.MINUTE, 1);



                Thread.sleep(2000);
                cal.add(Calendar.MINUTE, 1);

                //}
            } catch(Exception e){
                e.printStackTrace();
            }
        }

        channel.close();
        connection.close();

    }

}
