package rabbitMQ;

import com.appnomic.appsone.common.protbuf.A1EventProtos;
import com.appnomic.appsone.common.protbuf.NotificationProtos;
import com.rabbitmq.client.Channel;
import com.rabbitmq.client.Connection;
import com.rabbitmq.client.ConnectionFactory;

import java.io.ByteArrayOutputStream;

public class SendNotification {

    public static void main(String[] args) {

        NotificationProtos.UserDetails user = NotificationProtos.UserDetails.newBuilder()
                //.setContactNumber("***********")
                .setEmailAddress("<EMAIL>")
                .build();
        /*NotificationProtos.Notification notif = NotificationProtos.Notification.newBuilder()
                .setSourceType("Forensic")
                .setAccountId("qa-d681ef13-d690-4917-jkhg-6c79b-1")
                .addUsersDetailsList(user)
                .putPlaceHolders("Timezone", "********")
                .putPlaceHolders("Latest_Events", "AE-3-9-397-G-*********")
                .putPlaceHolders("Latest_Event_Time", "2020-05-12 15:22:47")
                .putPlaceHolders("Organization_Name", "INDIA")
                .putPlaceHolders("Latest_Events_Detected", "Service Name: ENET-DB-Service, Request Name: GET#/http104branchserver.aspx|srv=LOS-Web-Service|acc=3, Host address:keycloak.appnomic, KPI name: Fail, KPI attribute: ALL, Value: 37320.0, Unit: Count, Operation: not between, Lower threshold: 557.91, Upper Threshold: 1033.81")
                .putPlaceHolders("Signal_Type", "EarlyWarning")
                .putPlaceHolders("StartTime", "2020-05-12 15:22:47")
                .putPlaceHolders("Severity",  "NON_CRITICAL")
                .putPlaceHolders("A1Protocol", "https")
                .putPlaceHolders("Signal_Description", "Early Warning: Transaction performance may get affected due to issues in services")
                .putPlaceHolders("Total_Events", "1")
                .putPlaceHolders("A1Port", "8443")
                .putPlaceHolders("Signal_Status", "OPEN")
                .putPlaceHolders("RootCause_ServiceNames", "ENET-DB-Service")
                .putPlaceHolders("Username", "")
                .putPlaceHolders("Affected_ServiceNames", "ENET-DB-Service")
                .putPlaceHolders("AccountIdentifier", "d681ef13-d690-4917-jkhg-6c79b-1")
                .putPlaceHolders("Signal_ID", "E-2-403-14-********")
                .putPlaceHolders("App_Names", "enet_3")
                .putPlaceHolders("Affected_ApplicationNames", "enet_3")
                .putPlaceHolders("A1HostAddress", "keycloak.appnomic")
                .build();*/

        NotificationProtos.Attachment attachment = NotificationProtos.Attachment.newBuilder()
                .setName("Forensic_fetch_disk_io_AE2-70-16-T-vdc1-********.html")
                .setContentType("text/html")
                .setContent("Name: sar#@#@#APPSONE_NEWLINE#@#@#Description: Disk Utilization and performance#@#@#APPSONE_NEWLINE#@#@#Start Time: 09-17-2021, 04:46:10#@#@#APPSONE_NEWLINE#@#@#End Time: 09-17-2021, 04:46:11#@#@#APPSONE_NEWLINE#@#@#Execute Status: Failed#@#@#APPSONE_NEWLINE#@#@#ReturnCode: E5001#@#@#APPSONE_NEWLINE#@#@#KeyValueOutput: #@#@#APPSONE_NEWLINE#@#@#Output:Error: Invalid number of arguments received")
                .build();
        NotificationProtos.Notification notif = NotificationProtos.Notification.newBuilder()
                .setSourceType("Forensic")
                .setAccountId("qa-d681ef13-d690-4917-jkhg-6c79b-1")
                .addUsersDetailsList(user)
                .addAttachments(attachment)
                .putPlaceHolders("CategoryId", "Disk IO")
                .putPlaceHolders("Lower", "4.0")
                .putPlaceHolders("agentType", "ForensicAgent")
                .putPlaceHolders("AccountId", "qa-d681ef13-d690-4917-jkhg-6c79b-1")
                .putPlaceHolders("Anomaly_Event_ID", "AE2-70-16-T-vdc1-********")
                .putPlaceHolders("ActionTriggerTime", "*************")
                .putPlaceHolders("Operation", "not between")
                .putPlaceHolders("KPIName",  "Device Busy")
                .putPlaceHolders("remote_user", "")
                .putPlaceHolders("SupervisorMode", "LOCAL")
                .putPlaceHolders("Signal_Status", "Forensic")
                .putPlaceHolders("Event_Detected_Time", "*************")
                .putPlaceHolders("Affected_ServiceNames", "[ENET-DB-Service-DC, ENET-App-Service-DC, ENET-Web-Service-DC]")
                .putPlaceHolders("KPIAttribute", "vdc1")
                .putPlaceHolders("App_Names", "[enet_3_DC]")
                .putPlaceHolders("InstanceRowId", "70")
                .putPlaceHolders("Upper", "6.0")
                .putPlaceHolders("IsWorkloadKpi", "false")
                .putPlaceHolders("InstanceId", "SOLARIS_ENET_HOST_112_Inst_1-DC")
                .putPlaceHolders("ProducerType", "SCRIPT")
                .putPlaceHolders("remote_port", "22")
                .putPlaceHolders("Severity", "Default")
                .putPlaceHolders("KPIValue", "0.0")
                .putPlaceHolders("ip_address", "**************")
                .putPlaceHolders("sample", "3")
                .putPlaceHolders("InstanceName", "SOLARIS_ENET_HOST_112_Inst_1-DC")
                .putPlaceHolders("ActionId", "action-oob-diskio")
                .putPlaceHolders("isInformatic", "0.0")
                .putPlaceHolders("KPIIdentifier", "DEVICE_BUSY")
                .putPlaceHolders("CategoryName", "Disk IO")
                .putPlaceHolders("KPIId", "16")
                .putPlaceHolders("interval", "3")
                .putPlaceHolders("Forensic_Name", "fetch_disk_io")
                .build();

        A1EventProtos.A1Event event = A1EventProtos.A1Event.newBuilder()
                .setEventData(notif.toByteString())
                .setEventType("NOTIFICATION_OUTPUT")
                .build();
        try {
            ConnectionFactory factory = new ConnectionFactory();
            factory.setHost("**************");
            factory.setPort(5672);
            factory.setVirtualHost("/");
            factory.useSslProtocol();
            Connection connection = factory.newConnection();
            Channel channel = connection.createChannel();

            channel.queueDeclare("a1-event-messages", true, false, false, null);

            ByteArrayOutputStream os = new ByteArrayOutputStream();
            event.writeTo(os);
            channel.basicPublish("", "a1-event-messages", null, os.toByteArray());
            System.out.println("Data Inserted ");

            channel.close();
            connection.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
