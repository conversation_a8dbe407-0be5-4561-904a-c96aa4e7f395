syntax = "proto3";

option java_package = "com.appnomic.appsone.common.protbuf";
option java_outer_classname = "SignalProtos";
 
message SignalDetails {
	string accountId = 1;
	string signalType = 2; //PROBLEM, EARLY-WARNING, INFO, BATCH
	string signalId = 3;
	string signalStatus = 4;
	bool isRemainder = 5;
	bool isSeverityChanged = 6;
	bool isServiceAdded = 7;
	map<string, string> metadata = 8;
	repeated AnomalyDetail anomalyDetails = 9;
	repeated string relatedSignals = 10;
	int64 startTime = 11;
	int64 updateTime = 12;
	repeated string serviceIds = 13;
	repeated string rootCauseServiceIds = 14;
	repeated string entryServiceIds = 15;
	int64 signalSeverityId = 16;
	int64 anomalies=17;
	repeated ServiceSummary serviceSummary = 18;
	repeated InstanceSummary instanceSummary = 19;
	repeated RequestSummary requestSummary = 20;
}

message AnomalyDetail {
	string anomalyId = 1;
	int64 severityId = 2;
	bool isWorkLoad = 3;
	string instanceId = 4;
	string kpiId = 5;
	string kpiGroupId = 6;
	string categoryId = 7;
	string kpiAttribute = 8;
	int64 anomalyTime = 9;
	repeated string serviceIds = 10;
	string kpiValue = 11;
	string thresholdType = 12;
	string operationType = 13;
	map<string, double> thresholds = 14;
	map<string, string> metadata = 15;
}

message ServiceSummary {
	string serviceId = 1;
	repeated string severeAnomalies = 2;
	repeated string defaultAnomalies = 3;
	repeated string instanceIds = 4;
	repeated string requestIds = 5;
	map<string, string> categories = 6;
	int64 anomalyTime = 7;
}

message InstanceSummary {
	string instanceId = 1;
	map<string, string> kpiSeverity = 2;
	map<string, string> kpiCategories = 3;
	map<string, int64> kpiLatestTime = 4;
	repeated string severeAnomalies = 5;
	repeated string defaultAnomalies = 6;
	int64 anomalyTime = 7;
}

message RequestSummary {
	string transactionId = 1;
	repeated string severeAnomalies = 2;
	repeated string defaultAnomalies = 3;
	map<string, string> kpiCategories = 4;
	map<string, string> kpiSeverity = 5;
	map<string, int64> kpiLatestTime = 6;
	int64 anomalyTime = 7;
	string serviceId = 8;
}