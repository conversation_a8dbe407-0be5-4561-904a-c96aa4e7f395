package rabbitMQ;

import java.io.IOException;
import java.util.concurrent.TimeoutException;

public class SendToQueue {
    private final static String QUEUE_NAME = "command-messages";

    /*public static void main(String[] args) throws IOException, TimeoutException, KeyStoreException, CertificateException, NoSuchAlgorithmException, UnrecoverableKeyException, KeyManagementException {

        char[] keyPassphrase = "serverpw".toCharArray();
        KeyStore ks = KeyStore.getInstance("PKCS12");
        ks.load(new FileInputStream("C:/Work_related/test-certificates/rabbitmq/actualOnes/appnomic-keystore.p12"), keyPassphrase);

        KeyManagerFactory kmf = KeyManagerFactory.getInstance("SunX509");
        kmf.init(ks, keyPassphrase);

        char[] trustPassphrase = "changeit".toCharArray();
        KeyStore tks = KeyStore.getInstance("JKS");
        tks.load(new FileInputStream("C:/Software/jdk1.8.0_211/jre/lib/security/cacerts"), trustPassphrase);

        TrustManagerFactory tmf = TrustManagerFactory.getInstance("SunX509");
        tmf.init(tks);

        SSLContext c = SSLContext.getInstance("TLSv1.2");
        c.init(kmf.getKeyManagers(), tmf.getTrustManagers(), null);

        ConnectionFactory factory = new ConnectionFactory();
        factory.setHost("**************");
        factory.setPort(5672);
        factory.useSslProtocol(c);

        Connection connection = factory.newConnection();
             Channel channel = connection.createChannel();
            channel.queueDeclare(QUEUE_NAME, true, false, false, null);
            String message = "Hello World!";
        CommandProtos.Command command2 = CommandProtos.Command.newBuilder()
                .setHostname("app server 1")
                .setSupervisorIdentifier("de0f2da3-772d-443d-ba67-30152a863475")
                .setAgentType("ForensicAgent")
                .setAgentIdentifier("f69aeb7b-67d4-4f1e-a827-54c0813faac4")
                .setCommandJobId("0cef1a75-f8f0-426e-9f15-56d0fc36f9f2")
                .setCommandType("Execute")
                .setCommand("cpu_util_ps")
                .setCommandOutputType("Blob")
                .setCommandExecType("ds.LongPolling")
                .setTriggerSource("Forensic Pipeline")
                .setUserDetailsID("Alex")
                .putArguments("arg1", "val1")
                .putArguments("arg2", "val2")
                .putEnvArgs("env1", "val1")
                .putEnvArgs("env2", "val2")
                .setCommandTimeout(20000)
                .setTriggerTime(561699502)
                .setSupervisorCtrlTTL(System.currentTimeMillis()+300000)
                .setViolationTime(561699000)
                .setRetryNumber(3)
                .putMetadata("description", "Dummy command")
                .putMetadata("version", "1.1")
                .build();

            CommandResponseProtos.CommandResponse resp = CommandResponseProtos.CommandResponse
                    .newBuilder()
                    .setCommandJobId("commandJobID-123")
                    .setAgentIdentifier("Forensic-Agent-123")
                    .setAgentType("Forensic-Agent")
                    .setCommandType("Execute")
                    .setTriggerSource("Forensic_pipeline")
                    .setCmdOut("A very long string!")
                    // .setStdErr("Error string")
                    .setExitCode(0)
                    .setCommandStartTime(System.currentTimeMillis())
                    .setCommandStartTime(System.currentTimeMillis()+10000)
                    .putMetadata("key1", "Key1-Value1")
                    .putMetadata("Key2", "Key2-Value2")
                    .build();

            A1EventProtos.A1Event event = A1EventProtos.A1Event.newBuilder()
                    .setEventType("FORENSIC")
                    .setEventData(resp.toByteString())
                    .build();

            channel.basicPublish("", QUEUE_NAME, null, command2.toByteArray());
            System.out.println(" [x] Sent '" + message + "'");

    }*/

        public static void main(String[] args) throws IOException, TimeoutException {
            /*ConnectionFactory factory = new ConnectionFactory();
            factory.setHost("127.0.0.1");
            factory.setPort(5672);

            try (Connection connection = factory.newConnection();
                 Channel channel = connection.createChannel()) {
                channel.queueDeclare(QUEUE_NAME, true, false, false, null);
                String message = "Hello World!";
                CommandProtos.Command command2 = CommandProtos.Command.newBuilder()
                        .setHostname("app server 1")
                        .setSupervisorIdentifier("265b168f-f7c6-44f1-8d8b-546549d3a802")
                        .setAgentType("ForensicAgent")
                        .setAgentIdentifier("e570de02-c585-4917-bbb7-5c97b35e-5")
                        .setCommandJobId("0cef1a75-f8f0-426e-9f15-56d0fc36f1062")
                        .setCommandType("Execute")
                        .setCommand("fetch_mem_util")
                        .setCommandOutputType("Blob")
                        .setCommandExecType("Execute")
                        .setTriggerSource("ForensicPipeline")
                        .setUserDetailsID("7640123a-fbde-4fe5-9812-581cd1e3a9c1")
                        .putArguments("record_count", "10")
                        .putArguments("trace_duration", "5")
                        .putEnvArgs("env1", "val1")
                        .putEnvArgs("env2", "val2")
                        .setCommandTimeout(180)
                        .setTriggerTime(System.currentTimeMillis() / 1000)
                        .setSupervisorCtrlTTL(300)
                        .setViolationTime(System.currentTimeMillis() / 1000)
                        .setRetryNumber(3)
                        .putMetadata("AccountId", "Acc-1")
                        .putMetadata("InstanceId", "Inst-1")
                        .putMetadata("CategoryId", "CId-2")
                        .putMetadata("ForensicId", "FId-1")
                        .putMetadata("ForensicEventTime", System.currentTimeMillis() + "")
                        .putMetadata("IsWorkLoad", "Yes")
                        .putMetadata("KPIId", "1")
                        .putMetadata("KPIAttribute", "0")
                        .build();

                channel.basicPublish("", QUEUE_NAME, null, command2.toByteArray());
                System.out.println(" [x] Sent '" + message + "'");
            }*/
        }

}
