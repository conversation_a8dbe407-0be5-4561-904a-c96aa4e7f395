package rabbitMQ;

import com.appnomic.appsone.common.protbuf.ViolatedEventProtos;
import com.rabbitmq.client.Channel;
import com.rabbitmq.client.Connection;
import com.rabbitmq.client.ConnectionFactory;

import java.io.IOException;
import java.security.KeyManagementException;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.concurrent.TimeoutException;

public class SendViolatedEventToQueue {

    public static void main(String[] args) throws IOException, TimeoutException, KeyManagementException, NoSuchAlgorithmException {
        /*ConnectionFactory factory = new ConnectionFactory();
        factory.setHost("**************");
        factory.setPort(5672);
        factory.useSslProtocol();
        factory.setUsername("guest");
        factory.setPassword("guest");
        factory.setVirtualHost("/");*/

        /*try (Connection connection = factory.newConnection();
             Channel channel = connection.createChannel()) {
            channel.queueDeclare("violated-events", true, false, false, null);

            HashMap<String, Double> thresholdMap = new HashMap<>();
            thresholdMap.put("lower than", 0.0);
            thresholdMap.put("greater than", 10.0);
            ViolatedEventProtos.KpiInfo kpiInfo = ViolatedEventProtos.KpiInfo.newBuilder()
                    .setKpiId("1") //CompInstKpiId:12 and Id: 291
                    .setKpiAttribute("ALL")
                    .setInstanceId("RHEL_192.168.13.154_Inst_1")
                    .addSvcId("WebLogic-Service")
                    .putAllThresholds(thresholdMap)
                    .setOperationType("Upper")
                    .build();

            ViolatedEventProtos.Kpi kpi = ViolatedEventProtos.Kpi.newBuilder()
                    .setKpiInfo(kpiInfo)
                    .setValue("8")
                    .build();

            ViolatedEventProtos.TransactionInfo txnInfo = ViolatedEventProtos.TransactionInfo.newBuilder()
                    .setGroupId("0")
                    .setKpiId("397")
                    .setTransactionId("GET/seg1/seg2")
                    .setSvcId("WebLogic-Service")
                    .setResponseTimeType(PSAgentMessageProtos.ResponseTime.ResponseTimeType.DC)
                    .setOperationType("Upper")
                    .putAllThresholds(thresholdMap)
                    .build();

            ViolatedEventProtos.Transaction txn = ViolatedEventProtos.Transaction.newBuilder()
                    .setTxnInfo(txnInfo)
                    .setValue("7")
                    .build();

            List<ViolatedEventProtos.Kpi> kpiList = new ArrayList<>();
            kpiList.add(kpi);

            List<ViolatedEventProtos.Transaction> txnList = new ArrayList<>();
            txnList.add(txn);

            ViolatedEventProtos.ViolatedEvent ve = ViolatedEventProtos.ViolatedEvent.newBuilder()
                    .setAccountId("d681ef13-d690-4917-jkhg-6c79b-1")
                    .setAppId("ENetbanking")
                    .setTimezoneOffsetInSeconds(0)
                    .setThresholdType("Realtime")
                    .addAllKpis(kpiList)
                    .addAllTransactions(txnList)
                    .setViolationTmeInGMT("2019-09-20 15:26:00")
                    .build();

            channel.basicPublish("", "violated-events", null, ve.toByteArray());*/
        ConnectionFactory factory = new ConnectionFactory();
        //factory.useSslProtocol();
        factory.setHost("127.0.0.1");
        //factory.setVirtualHost("/");
        factory.setPort(5672);
        Connection connection = factory.newConnection();
        Channel channel = connection.createChannel();
        channel.queueDeclare("violated-events", true, false, false, null);

      /*  DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:00");
        dateFormat.setTimeZone(TimeZone.getTimeZone("GMT"));
        Calendar cal = Calendar.getInstance();*/
        //cal.setTimeInMillis(*********0000l);
       // String StartTimeInGMT = dateFormat.format(cal.getTime());

           /* HashMap<String, Double> thresholdMap = new HashMap<>();
            thresholdMap.put("lower than", 10.0);
            thresholdMap.put("greater than", 20.0);
            ViolatedEventProtos.KpiInfo kpiInfo = ViolatedEventProtos.KpiInfo.newBuilder()
                    .setKpiId("1") //CompInstKpiId:12 and Id: 291
                    .setKpiAttribute("ALL")
                    .setInstanceId("RHEL_192.168.13.154_Inst_1")
                    .addSvcId("WebLogic-Service")
                    .putAllThresholds(thresholdMap)
                    .build();
            *//*ViolatedEventProtos.kpi kpi = ViolatedEventProtos.kpi.newBuilder()
                    .setKpiInfo(kpiInfo)
                    .setValue("1")
                    .build();*//*
            ViolatedEventProtos.Kpi kpi = ViolatedEventProtos.Kpi.newBuilder()
                    .setKpiInfo(kpiInfo)
                    .setValue("1")
                    .build();
            List<ViolatedEventProtos.Kpi> kpiList = new ArrayList<>();
            kpiList.add(kpi);
            *//*ViolatedEventProtos.TransactionInfo txnInfo = ViolatedEventProtos.TransactionInfo.newBuilder()
                    .setGroupId("Default")
                    .setKpiId("ALL")
                    .setTransactionId("POST/seg0/seg1")
                    .setSvcId("WebLogic-Service")
                    .putAllThresholds(thresholdMap)
                    .build();
            ViolatedEventProtos.Transaction txn = ViolatedEventProtos.Transaction.newBuilder()
                    .setTxnInfo(txnInfo)
                    .setValue("1")
                    .build();
            List<ViolatedEventProtos.Transaction> txnList = new ArrayList<>();
            txnList.add(txn);*//*

            ViolatedEventProtos.ViolatedEvent ve = ViolatedEventProtos.ViolatedEvent.newBuilder()
                    .setAccountId("d681ef13-d690-4917-jkhg-6c79b-1")
                    .setAppId("enetbanking_1")
                    .setTimezoneOffsetInSeconds(0) //dont change
                    .setViolationType(ViolatedEventProtos.ViolatedEvent.Violationtype.DYNAMIC)
                    .addAllKpis(kpiList)
                    //.addAllTransactions(txnList)
                    .setViolationTmeInGMT(StartTimeInGMT)
                    .build();*/

        // for (int i = 1; i <= 1; i++) {

           /* thresholdMap.put("lower than", 10.0);
            thresholdMap.put("greater than", 0.0);
            ViolatedEventProtos.KpiInfo kpiInfo = ViolatedEventProtos.KpiInfo.newBuilder()
                    .setKpiId("17") //CompInstKpiId:12 and Id: 291
                    .setKpiAttribute("ALL")
                    .setInstanceId("RHEL_192.168.13.154_Inst_1")
                    .addSvcId("WebLogic-Service")
                    .putAllThresholds(thresholdMap)
                    .setOperationType("greater than")
                    .build();
            ViolatedEventProtos.Kpi kpi = ViolatedEventProtos.Kpi.newBuilder()
                    .setKpiInfo(kpiInfo)
                    .setValue("20")
                    .build();*/

            /*ViolatedEventProtos.TransactionInfo txnInfo = ViolatedEventProtos.TransactionInfo.newBuilder()
                    .setGroupId("0")
                    .setKpiId("397")
                    .setTransactionId("GET/seg1/seg2")
                    .setSvcId("WebLogic-Service")
                    .setResponseTimeType(PSAgentMessageProtos.ResponseTime.ResponseTimeType.DC)
                    .setOperationType("greater than")
                    .putAllThresholds(thresholdMap)
                    .build();
            ViolatedEventProtos.Transaction txn = ViolatedEventProtos.Transaction.newBuilder()
                    .setTxnInfo(txnInfo)
                    .setValue("7")
                    .build();
            List<ViolatedEventProtos.Transaction> txnList = new ArrayList<>();
            txnList.add(txn);*/


       // StartTimeInGMT = dateFormat.format(Calendar.getInstance().getTime());
        HashMap<String, Double> thresholdMap = new HashMap<>();
        thresholdMap.put("Upper", 6.0);
        thresholdMap.put("Lower", 4.0);
        ViolatedEventProtos.KpiInfo kpiInfo = ViolatedEventProtos.KpiInfo.newBuilder()
                .setKpiId("16") //CompInstKpiId:12 and Id: 291
                .setKpiAttribute("vdc1")
                .setInstanceId("SOLARIS_ENET_HOST_112_Inst_1-DC")
                .addSvcId("ENET-App-Service-DC")
                .putAllThresholds(thresholdMap)
                .setOperationType("not between")
                .build();
        ViolatedEventProtos.Kpi kpi = ViolatedEventProtos.Kpi.newBuilder()
                .setKpiInfo(kpiInfo)
                .setValue("28.52")
                .build();
        List<ViolatedEventProtos.Kpi> kpiList = new ArrayList<>();
        kpiList.add(kpi);

        ViolatedEventProtos.ViolatedEvent ve = ViolatedEventProtos.ViolatedEvent.newBuilder()
                .setAccountId("qa-d681ef13-d690-4917-jkhg-6c79b-1")
                .addAppId("enet_3_DC")
                .setTimezoneOffsetInSeconds(********)
                .setThresholdType("Static")
                .addAllKpis(kpiList)
                .setViolationTmeInGMT("2021-09-17 18:00:00")
                .build();
        channel.basicPublish("", "violated-events", null, ve.toByteArray());
       // System.out.println(" [x] Sent" + StartTimeInGMT);
        //Thread.sleep(59000);
        //}
        channel.close();
        connection.close();

        System.out.println(" [x] Sent");
    }
}

