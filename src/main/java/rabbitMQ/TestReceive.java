package rabbitMQ;

import com.appnomic.appsone.common.protbuf.AggregatedKpiProtos;
import com.appnomic.appsone.common.protbuf.KPIAgentMessageProtos;
import com.rabbitmq.client.AMQP;
import com.rabbitmq.client.Channel;
import com.rabbitmq.client.Connection;
import com.rabbitmq.client.ConnectionFactory;

import java.io.ByteArrayOutputStream;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.IntStream;

public class TestReceive {
    private final static String AGGREGATED_KPI_QUEUE_NAME = "static-aggregated-kpi";
    private static final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    public static void main(String[] args) throws Exception
    {
        ConnectionFactory factory = new ConnectionFactory();
        factory.useSslProtocol();
        factory.setHost("**************");
        factory.setVirtualHost("/");
        factory.setPort(5672);
        Connection connection = factory.newConnection();
        Channel channel = connection.createChannel();
        channel.queueDeclare(AGGREGATED_KPI_QUEUE_NAME, true, false, false, null);
        AMQP.BasicProperties persistent = new AMQP.BasicProperties.Builder().deliveryMode(2).build();
        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        dateFormat.setTimeZone(TimeZone.getTimeZone("GMT"));
        Calendar cal = Calendar.getInstance();
        /**
         * ==========================publish data===========================
         */
        //cal.set(2019, Calendar.AUGUST, 22, 12, 32, 00);
        int m = 0;
        while (m < 1){
            //publishNonGrpKpi(persistent, channel, cal, dateFormat, "d681ef13-d690-4917-jkhg-6c79b-1", "NetBanking", "RHEL_NB_App_Host_146_Inst_1", 1, "Open Sockets", "30", 60, 112);
            getGrpAggKpis(persistent, channel, cal, dateFormat, "d681ef13-d690-4917-jkhg-6c79b-1", "NetBanking", "RHEL_NB_App_Host_146_Inst_1", 1, "Total Size (MB)", "50", 3600);
            /*cal.add(Calendar.MINUTE, 20);
            publishNonGrpKpi(persistent, channel, cal, dateFormat, "8b9d77dd-64dc-4945-856d-b294b89c2cae", "corebanking_2", "AWS", 10, "TotalSpace", "40", 60,1);
            cal.add(Calendar.MINUTE, 20);
            publishNonGrpKpi(persistent, channel, cal, dateFormat, "8b9d77dd-64dc-4945-856d-b294b89c2cae", "corebanking_2", "AWS", 16, "TotalSpace", "40", 60,1);
            cal.add(Calendar.MINUTE, 20);*/
            m++;
        }
        channel.close();
        connection.close();
    }
    private static void publishNonGrpKpi(AMQP.BasicProperties persistent, Channel channel, Calendar cal, DateFormat dateFormat, String accountId, String applicationid, String instanceId, int count, String kpiName, String kpiVal, int collectionInterval, int kpiUid){
        int m = 0;
        while (m < count) {
            String startTimeInGMT = dateFormat.format(cal.getTime());
            List<String> list = new ArrayList<>();
            //list.add("3");
            list.add("NB-App-Service");

            //list.add("5");
            AggregatedKpiProtos.AggregatedKpi aggregatedKpi = AggregatedKpiProtos.AggregatedKpi.newBuilder()
                    .setAccountId(accountId)
                    //.setApplicationId(applicationid)
                    .setInstanceId(instanceId)
                    //.setServiceId(3, "WebLogic-Service")
                    .setTimeZoneOffsetInSec(19800)
                    .addAllServiceId(list)
                    .setTimeInGMT(startTimeInGMT)
                    .setKpiData(KPIAgentMessageProtos.KPIAgentMessage.KpiData
                            .newBuilder()
                            .setKpiUid(kpiUid)
                            .setTimeInGMT(startTimeInGMT)
                            .setKpiType(KPIAgentMessageProtos.KPIAgentMessage.KpiData.KpiType.Core)
                            .setKpiName(kpiName)
                            .setVal(kpiVal)
                            .setCollectionInterval(collectionInterval)
                            .setIsKpiGroup(false)
                            .build()).build();
            System.out.println("msg  " + aggregatedKpi);
            IntStream.range(0, 1).forEach(t -> {
                try {
                    ByteArrayOutputStream os = new ByteArrayOutputStream();
                    aggregatedKpi.writeTo(os);
                    channel.basicPublish("", AGGREGATED_KPI_QUEUE_NAME, persistent, os.toByteArray());
                } catch (Exception e) {
                    e.printStackTrace();
                }
            });
            cal.add(Calendar.MINUTE, 1);
            m++;
        }
    }
    public static void getGrpAggKpis(AMQP.BasicProperties persistent, Channel channel, Calendar cal, DateFormat dateFormat, String accountId, String applicationid, String instanceId, int count, String kpiName, String kpiVal, int collectionInterval){
        int m = 0;
        while (m < count) {
            Map<String, String> pairs = new HashMap<>();
            //pairs.put(kpiName, kpiVal);
            pairs.put("/", "10");
            pairs.put("/dev", "20");
            pairs.put("/export", "30");
            //pairs.put("B", "35");
            KPIAgentMessageProtos.KPIAgentMessage.KpiData.GroupKpi.Builder grpBuilder = KPIAgentMessageProtos.KPIAgentMessage.KpiData.GroupKpi.newBuilder();
            grpBuilder.putAllPairs(pairs);
            KPIAgentMessageProtos.KPIAgentMessage.KpiData.GroupKpi groupKpi = grpBuilder.build();
            List<String> list = new ArrayList<>();
            list.add("NB-App-Service");
            String startTimeInGMT = dateFormat.format(cal.getTime());
            AggregatedKpiProtos.AggregatedKpi aggregatedKpi = AggregatedKpiProtos.AggregatedKpi.newBuilder()
                    .setAccountId(accountId)
                   // .setApplicationId(applicationid)
                    .setInstanceId(instanceId)
                    .setTimeZoneOffsetInSec(19800)
                    .addAllServiceId(list)
                    .setTimeInGMT(startTimeInGMT)
                    .setKpiData(KPIAgentMessageProtos.KPIAgentMessage.KpiData
                            .newBuilder()
                            .setKpiUid(17)
                            .setTimeInGMT(startTimeInGMT)
                            //.setKpiType(KPIAgentMessageProtos.KPIAgentMessage.KpiData.KpiType.Core)
                            .setKpiName(kpiName)
                            .setGroupKpi(groupKpi)
                            .setCollectionInterval(collectionInterval)
                            .setIsKpiGroup(true)
                            .build()).build();
            System.out.println("msg  " + aggregatedKpi);
            IntStream.range(0, 1).forEach(t -> {
                try {
                    ByteArrayOutputStream os = new ByteArrayOutputStream();
                    aggregatedKpi.writeTo(os);
                    channel.basicPublish("", AGGREGATED_KPI_QUEUE_NAME, persistent, os.toByteArray());
                } catch (Exception e) {
                    e.printStackTrace();
                }
            });
            cal.add(Calendar.MINUTE, 1);
            m++;
        }
    }
    private static String getDate(long timeInMillis){
        ZonedDateTime dateTime = Instant.ofEpochMilli(timeInMillis).atZone(ZoneId.of("Australia/Sydney"));
        return formatter.format(dateTime);
    }
}