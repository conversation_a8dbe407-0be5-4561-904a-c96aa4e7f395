package rabbitMQ;

import com.appnomic.appsone.common.protbuf.SignalProtos;
import com.appnomic.appsone.common.protbuf.SignalProtos.AnomalyDetail;
import com.appnomic.appsone.common.protbuf.SignalProtos.InstanceSummary;
import com.appnomic.appsone.common.protbuf.SignalProtos.ServiceSummary;
import com.rabbitmq.client.Channel;
import com.rabbitmq.client.Connection;
import com.rabbitmq.client.ConnectionFactory;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.TimeoutException;

/**
 * Builds and publishes a SignalProtos.SignalDetails message to the "signal-messages" queue.
 * NOTE: Field/method names are inferred. Adjust if the generated proto differs (e.g. setSeverityChanged vs setIsSeverityChanged, addServiceIds vs addServiceId).
 */
public class SendSignalDetails {
    public static void main(String[] args) throws IOException, TimeoutException {
        // Top-level metadata map
        Map<String, String> metadata = new LinkedHashMap<>();
        metadata.put("AnomalyId", "AE-3-64-59-C-N-64897-********");
        metadata.put("accountIdentifiers", "demo");
        metadata.put("anomalyLevel", "INSTANCE");
        metadata.put("anomalyScore", "0.27");
        metadata.put("anomalyStatus", "Open");
        metadata.put("appIds", "Service_App_1,Service_App_2");
//        metadata.put("attributeName", "ALL");
//        metadata.put("closeWindowResetCount", "0");
//        metadata.put("closingWindow", "0");
//        metadata.put("dataBreakResetCount", "0");
        metadata.put("eventId", "AE-3-64-59-C-N-64897-********-001");
        metadata.put("eventOccurrenceCount", "1");
        metadata.put("eventStatus", "Open");
//        metadata.put("eventType", "persistence");
//        metadata.put("high_persistence", "1");
//        metadata.put("high_persistenceMeetCount", "0");
//        metadata.put("high_suppression", "1");
//        metadata.put("high_suppressionMeetCount", "0");
//        metadata.put("high_thresholdOperation", "greater than");
//        metadata.put("high_thresholdValue", "0");
//        metadata.put("high_violationsResetCount", "0");
//        metadata.put("isGroupKpi", "true");
//        metadata.put("isInformatic", "0");
//        metadata.put("isMaintenanceExcluded", "1");
//        metadata.put("kpiType", "Core");
//        metadata.put("lastMaintenanceExcluded", "1756201020000");
//        metadata.put("low_persistence", "3");
//        metadata.put("low_persistenceMeetCount", "2");
//        metadata.put("low_suppression", "2");
//        metadata.put("low_suppressionMeetCount", "0");
//        metadata.put("low_thresholdOperation", "greater than");
//        metadata.put("low_thresholdValue", "2");
//        metadata.put("low_violationsResetCount", "0");
//        metadata.put("maxDataBreaks", "0");
//        metadata.put("medium_persistence", "2");
//        metadata.put("medium_persistenceMeetCount", "1");
//        metadata.put("medium_suppression", "2");
//        metadata.put("medium_suppressionMeetCount", "0");
//        metadata.put("medium_thresholdOperation", "greater than");
//        metadata.put("medium_thresholdValue", "2");
//        metadata.put("medium_violationsResetCount", "0");
//        metadata.put("persistence", "2");
//        metadata.put("remaindersCount", "0");
        metadata.put("serviceIdentifier", "Service_App_1, Service_App_2");
//        metadata.put("suppression", "2");
//        metadata.put("thresholdsLower", "78.0");
//        metadata.put("thresholdsUpper", "0.0");
        metadata.put("value", "89.0");
        metadata.put("violationLevel", "INSTANCE");

        // Helper to build anomalyDetails metadata maps
        Map<String, String> anomalyMetaTemplate = new LinkedHashMap<>();
        anomalyMetaTemplate.put("accountIdentifier", "demo");
        anomalyMetaTemplate.put("anomalyLevel", "INSTANCE");
        anomalyMetaTemplate.put("anomalyStatus", "Open");
        anomalyMetaTemplate.put("attributeName", "ALL");
//        anomalyMetaTemplate.put("closeWindowResetCount", "0");
//        anomalyMetaTemplate.put("closingWindow", "0");
//        anomalyMetaTemplate.put("dataBreakResetCount", "0");
//        anomalyMetaTemplate.put("high_persistence", "1");
//        anomalyMetaTemplate.put("high_persistenceMeetCount", "0");
//        anomalyMetaTemplate.put("high_suppression", "1");
//        anomalyMetaTemplate.put("high_suppressionMeetCount", "0");
//        anomalyMetaTemplate.put("high_thresholdOperation", "greater than");
//        anomalyMetaTemplate.put("high_thresholdValue", "0");
//        anomalyMetaTemplate.put("high_violationsResetCount", "0");
//        anomalyMetaTemplate.put("isGroupKpi", "true");
//        anomalyMetaTemplate.put("isInformatic", "0");
//        anomalyMetaTemplate.put("isMaintenanceExcluded", "1");
//        anomalyMetaTemplate.put("kpiType", "Core");
//        anomalyMetaTemplate.put("low_persistence", "3");
//        anomalyMetaTemplate.put("low_persistenceMeetCount", "2");
//        anomalyMetaTemplate.put("low_suppression", "2");
//        anomalyMetaTemplate.put("low_suppressionMeetCount", "0");
//        anomalyMetaTemplate.put("low_thresholdOperation", "greater than");
//        anomalyMetaTemplate.put("low_thresholdValue", "3");
//        anomalyMetaTemplate.put("low_violationsResetCount", "0");
//        anomalyMetaTemplate.put("maxDataBreaks", "0");
//        anomalyMetaTemplate.put("medium_persistence", "2");
//        anomalyMetaTemplate.put("medium_persistenceMeetCount", "0");
//        anomalyMetaTemplate.put("medium_suppression", "2");
//        anomalyMetaTemplate.put("medium_suppressionMeetCount", "0");
//        anomalyMetaTemplate.put("medium_thresholdOperation", "greater than");
//        anomalyMetaTemplate.put("medium_thresholdValue", "0");
//        anomalyMetaTemplate.put("medium_violationsResetCount", "0");
//        anomalyMetaTemplate.put("persistence", "3");
//        anomalyMetaTemplate.put("remaindersCount", "0");
//        anomalyMetaTemplate.put("suppression", "2");
//        anomalyMetaTemplate.put("thresholdsUpper", "0.0");
//        anomalyMetaTemplate.put("violationLevel", "INSTANCE");

        // Build individual anomaly details (values differing per anomaly)
        AnomalyDetail anomaly1 = AnomalyDetail.newBuilder()
                .setAnomalyId("AE-3-64-59-C-N-64897-********")
                .setSeverityId(433)
                .setInstanceId("0ac94752-9786-4cfe-9239-f0d7426b1c14")
                .setKpiId("CPULoad")
                .setCategoryId("WebserverCPU")
                .setKpiAttribute("ALL")
                .setAnomalyTime(1755874680000L)
                .addServiceIds("Service_App_2") // repeated field => use addServiceIds
                .setThresholdType("Static")
                .setOperationType("greater than")
                .putAllMetadata(merge(anomalyMetaTemplate, mapOf(
                        "anomalyScore", "0.27",
                        "appIds", "Service_App_2",
                        "eventId", "AE-3-64-59-C-N-64897-********-001",
                        "eventOccurrenceCount", "1",
                        "eventStatus", "Open",
                        "eventType", "persistence",
                        "thresholdsLower", "78.0",
                        "value", "89.0"
                )))
                .build();

//        AnomalyDetail anomaly2 = AnomalyDetail.newBuilder()
//                .setAnomalyId("AE-3-64-59-C-N-64897-********")
//                .setSeverityId(432)
//                .setInstanceId("0ac94752-9786-4cfe-9239-f0d7426b1c14")
//                .setKpiId("CPULoad")
//                .setCategoryId("WebserverCPU")
//                .setKpiAttribute("ALL")
//                .setAnomalyTime(1756202640000L)
//                .addServiceIds("Service_App_2")
//                .addServiceIds("Service_App_1")
//                .setThresholdType("Static")
//                .setOperationType("greater than")
//                .putAllMetadata(merge(anomalyMetaTemplate, mapOf(
//                        "anomalyScore", "0.27",
//                        "appIds", "Service_App_1,Service_App_2",
//                        "eventId", "AE-3-64-59-C-N-64897-********-003",
//                        "eventOccurrenceCount", "3",
//                        "eventStatus", "Update",
//                        "eventType", "suppression",
//                        "low_thresholdValue", "4",
//                        "medium_thresholdValue", "4",
//                        "low_suppressionMeetCount", "1",
//                        "medium_suppressionMeetCount", "1",
//                        "thresholdsLower", "78.0",
//                        "value", "89.0"
//                )))
//                .build();
//
//        AnomalyDetail anomaly3 = AnomalyDetail.newBuilder()
//                .setAnomalyId("AE-3-64-59-C-N-64897-********")
//                .setSeverityId(433)
//                .setInstanceId("0ac94752-9786-4cfe-9239-f0d7426b1c14")
//                .setKpiId("CPULoad")
//                .setCategoryId("WebserverCPU")
//                .setKpiAttribute("ALL")
//                .setAnomalyTime(1755867060000L)
//                .addServiceIds("Service_App_2")
//                .setThresholdType("Static")
//                .setOperationType("greater than")
//                .putAllMetadata(merge(anomalyMetaTemplate, mapOf(
//                        "anomalyScore", "0.03",
//                        "appIds", "Service_App_2",
//                        "eventId", "AE-3-64-59-C-N-64897-********-001",
//                        "eventOccurrenceCount", "1",
//                        "eventStatus", "Open",
//                        "eventType", "persistence",
//                        "low_thresholdValue", "3",
//                        "thresholdsLower", "68.0",
//                        "value", "69.0"
//                )))
//                .build();
//
//        AnomalyDetail anomaly4 = AnomalyDetail.newBuilder()
//                .setAnomalyId("AE-3-64-59-C-N-64897-********")
//                .setSeverityId(433)
//                .setInstanceId("0ac94752-9786-4cfe-9239-f0d7426b1c14")
//                .setKpiId("CPULoad")
//                .setCategoryId("WebserverCPU")
//                .setKpiAttribute("ALL")
//                .setAnomalyTime(1755863880000L)
//                .addServiceIds("Service_App_2")
//                .setThresholdType("Static")
//                .setOperationType("greater than")
//                .putAllMetadata(merge(anomalyMetaTemplate, mapOf(
//                        "anomalyScore", "0.03",
//                        "appIds", "Service_App_2",
//                        "eventId", "AE-3-64-59-C-N-64897-********-001",
//                        "eventOccurrenceCount", "1",
//                        "eventStatus", "Open",
//                        "eventType", "persistence",
//                        "low_thresholdValue", "3",
//                        "thresholdsLower", "68.0",
//                        "value", "69.0"
//                )))
//                .build();
//
//        AnomalyDetail anomaly5 = AnomalyDetail.newBuilder()
//                .setAnomalyId("AE-3-64-59-C-N-64897-********")
//                .setSeverityId(431)
//                .setInstanceId("0ac94752-9786-4cfe-9239-f0d7426b1c14")
//                .setKpiId("CPULoad")
//                .setCategoryId("WebserverCPU")
//                .setKpiAttribute("ALL")
//                .setAnomalyTime(**********000L)
//                .addServiceIds("Service_App_2")
//                .setThresholdType("Static")
//                .setOperationType("greater than")
//                .putAllMetadata(merge(anomalyMetaTemplate, mapOf(
//                        "anomalyScore", "0.03",
//                        "appIds", "Service_App_2",
//                        "eventId", "AE-3-64-59-C-N-64897-********-001",
//                        "eventOccurrenceCount", "1",
//                        "eventStatus", "Open",
//                        "eventType", "persistence",
//                        "low_thresholdValue", "3",
//                        "thresholdsLower", "68.0",
//                        "value", "69.0"
//                )))
//                .build();

        // Service summaries
        ServiceSummary svcSummary2 = ServiceSummary.newBuilder()
                .setServiceId("Service_App_2")
                .addSevereAnomalies("AE-3-64-59-C-N-64897-********")
                .addSevereAnomalies("AE-3-64-59-C-N-64897-********")
                .addSevereAnomalies("AE-3-64-59-C-N-64897-********")
                .addSevereAnomalies("AE-3-64-59-C-N-64897-********")
                .addInstanceIds("0ac94752-9786-4cfe-9239-f0d7426b1c14")
                .putCategories("CPULoad", "WebserverCPU")
                .setAnomalyTime(1756201020000L)
                .build();

        ServiceSummary svcSummary1 = ServiceSummary.newBuilder()
                .setServiceId("Service_App_1")
                .addInstanceIds("0ac94752-9786-4cfe-9239-f0d7426b1c14")
                .putCategories("CPULoad", "WebserverCPU")
                .setAnomalyTime(1756201020000L)
                .build();

        // Instance summary
        InstanceSummary instanceSummary = InstanceSummary.newBuilder()
                .setInstanceId("0ac94752-9786-4cfe-9239-f0d7426b1c14")
                .putKpiSeverity("CPULoad", "433")
                .putKpiCategories("CPULoad", "WebserverCPU")
                .putKpiLatestTime("CPULoad", 1756201020000L)
                .addSevereAnomalies("AE-3-64-59-C-N-64897-********")
                .addSevereAnomalies("AE-3-64-59-C-N-64897-********")
                .addSevereAnomalies("AE-3-64-59-C-N-64897-********")
                .addSevereAnomalies("AE-3-64-59-C-N-64897-********")
                .setAnomalyTime(1756201020000L)
                .build();

        SignalProtos.SignalDetails.Builder builder = SignalProtos.SignalDetails.newBuilder()
                .setAccountId("demo")
                .setSignalType("EARLY_WARNING")
                .setSignalId("E-3-59--*********-**********")
                .setSignalStatus("OPEN")
                .setIsSeverityChanged(true)
                .putAllMetadata(metadata)
                .addAnomalyDetails(anomaly1)
//                .addAnomalyDetails(anomaly2)
//                .addAnomalyDetails(anomaly3)
//                .addAnomalyDetails(anomaly4)
//                .addAnomalyDetails(anomaly5)
                .setStartTime(**********000L)
                .setUpdateTime(1756201020000L)
                .addServiceIds("Service_App_2")
                .addServiceIds("Service_App_1")
                .addRootCauseServiceIds("Service_App_2")
                .setSignalSeverityId(432)
                .setAnomalies(4)
                .addServiceSummary(svcSummary2)
                .addServiceSummary(svcSummary1)
                .addInstanceSummary(instanceSummary);

        SignalProtos.SignalDetails signalDetails = builder.build();
        System.out.println(signalDetails.toString().length());

        ConnectionFactory factory = new ConnectionFactory();
//        factory.setHost("127.0.0.1");
        factory.setHost("**************");
        factory.setPort(5672);
        try (Connection connection = factory.newConnection();
             Channel channel = connection.createChannel()) {
            channel.queueDeclare("signal-messages-aiops", true, false, false, null);
            ByteArrayOutputStream os = new ByteArrayOutputStream();
            signalDetails.writeTo(os);
            channel.basicPublish("", "signal-messages-aiops", null, os.toByteArray());
            System.out.println("SignalDetails published");
        }
    }

    // Utility methods (inlined to avoid extra classes)
    private static Map<String, String> mapOf(String... kv) {
        Map<String, String> m = new LinkedHashMap<>();
        for (int i = 0; i < kv.length; i += 2) m.put(kv[i], kv[i + 1]);
        return m;
    }
    private static Map<String, String> merge(Map<String, String> base, Map<String, String> override) {
        Map<String, String> out = new LinkedHashMap<>(base);
        out.putAll(override);
        return out;
    }
}
