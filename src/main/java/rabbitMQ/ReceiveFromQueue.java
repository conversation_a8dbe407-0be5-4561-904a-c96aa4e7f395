package rabbitMQ;

import com.appnomic.appsone.common.protbuf.CommandRequestProtos;
import com.rabbitmq.client.Channel;
import com.rabbitmq.client.Connection;
import com.rabbitmq.client.ConnectionFactory;
import com.rabbitmq.client.DeliverCallback;

import java.io.ByteArrayInputStream;
import java.io.InputStream;

public class ReceiveFromQueue {
    private final static String QUEUE_NAME = "command-messages";

    public static void main(String[] argv) throws Exception {
        ConnectionFactory factory = new ConnectionFactory();
        factory.setHost("**************");
        factory.setPort(5671);
        factory.useSslProtocol();

        Connection connection = factory.newConnection();
        Channel channel = connection.createChannel();

        channel.queueDeclare(QUEUE_NAME, true, false, false, null);
        System.out.println(" [*] Waiting for messages. To exit press CTRL+C");

//        AtomicReference<com.heal.configuration.protbuf.CommandRequestProtos.Command> event = new AtomicReference<>();
        DeliverCallback deliverCallback = (consumerTag, delivery) -> {
        InputStream is = new ByteArrayInputStream(delivery.getBody());
            CommandRequestProtos.Command cmd = CommandRequestProtos.Command.parseFrom(is);

            System.out.println(cmd);
            /*if(event.get().getEventType().equalsIgnoreCase("NOTIFICATION_OUTPUT")) {
                byte[] cmdRespArray = new byte[event.get().getEventData().size()];
                event.get().getEventData().copyTo(cmdRespArray, 0);
                
                NotificationProtos.Notification notification;
                try {
                    notification = NotificationProtos.Notification.parseFrom(cmdRespArray);

                    if("Forensic".equalsIgnoreCase(notification.getSourceType())) {
                        System.out.println(notification);
                        System.out.println("======================================================");
                    }
                } catch (InvalidProtocolBufferException e) {
                    e.printStackTrace();
                }
            }*/
        };
        channel.basicConsume(QUEUE_NAME, true, deliverCallback, consumerTag -> { });
    }
}



