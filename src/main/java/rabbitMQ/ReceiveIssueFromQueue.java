package rabbitMQ;

import com.appnomic.appsone.common.protbuf.KPIAgentMessageProtos;
import com.rabbitmq.client.Channel;
import com.rabbitmq.client.Connection;
import com.rabbitmq.client.ConnectionFactory;
import com.rabbitmq.client.DeliverCallback;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.security.KeyManagementException;
import java.security.NoSuchAlgorithmException;
import java.util.concurrent.TimeoutException;

public class ReceiveIssueFromQueue {

    public static void main(String[] args) throws IOException, TimeoutException, KeyManagementException, NoSuchAlgorithmException {
        ConnectionFactory factory = new ConnectionFactory();
        factory.setHost("*************");
        factory.setPort(5672);
        factory.useSslProtocol();
        Connection connection = factory.newConnection();
        Channel channel = connection.createChannel();

        channel.queueDeclare("component-messages", true, false, false, null);
        System.out.println(" [*] Waiting for messages. To exit press CTRL+C");

        DeliverCallback deliverCallback = (consumerTag, delivery) -> {
            //IssueProtos.Issue message = IssueProtos.Issue.parseFrom(delivery.getBody());
            KPIAgentMessageProtos.KPIAgentMessage message = KPIAgentMessageProtos.KPIAgentMessage.parseDelimitedFrom(new ByteArrayInputStream(delivery.getBody()));
            System.out.println(" [x] Received '" + message + "'");

            System.out.println("======================" + message);
        };
        channel.basicConsume("component-messages", true, deliverCallback, consumerTag -> { });
    }
}
