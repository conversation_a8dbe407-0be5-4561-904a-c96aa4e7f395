package rabbitMQ;

import com.appnomic.appsone.common.protbuf.AggregatedKpiProtos;
import com.appnomic.appsone.common.protbuf.KPIAgentMessageProtos;
import com.rabbitmq.client.Channel;
import com.rabbitmq.client.Connection;
import com.rabbitmq.client.ConnectionFactory;

import java.io.IOException;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeoutException;

public class StaticThreshold {

    public static void main(String[] args) throws IOException, TimeoutException {
        ConnectionFactory factory = new ConnectionFactory();
        factory.setHost("**************");
        factory.setPort(5672);
        Connection connection = factory.newConnection();
        Channel channel = connection.createChannel();
        channel.queueDeclare("static-aggregated-kpi", true, false, false, null);

        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:00");
        dateFormat.setTimeZone(TimeZone.getTimeZone("GMT"));
        Calendar cal = Calendar.getInstance();
        String StartTimeInGMT = dateFormat.format(cal.getTime());

        /*KPIAgentMessageProtos.KPIAgentMessage msg1 = KPIAgentMessageProtos.KPIAgentMessage.newBuilder()
                .setAgentUid("e570de02-c585-4917-bbb7-5c97b35e-2")
                .addInstances(KPIAgentMessageProtos.KPIAgentMessage.Instance
                        .newBuilder()
                        .setInstanceId("RHEL_NB_Finacle_Host_204_Inst_1")

                        .addKpiData(KPIAgentMessageProtos.KPIAgentMessage.KpiData
                                .newBuilder()
                                .setKpiName("CPU_UTIL")
                                .setVal(String.valueOf(10))
                                .setKpiUid(1)
                                .setTimeInGMT(StartTimeInGMT)
                                .setKpiType(KPIAgentMessageProtos.KPIAgentMessage.KpiData.KpiType.Core)
                                .setIsKpiGroup(false)
                                .setCollectionInterval(60)
                                .build())
`
                        .build())
                .build();*/
        List<String> list = new ArrayList<>();
        list.add("Service_App_2");

        List<String> appList = new ArrayList<>();
        appList.add("Service_App_2");

        Map<String, String> map = new HashMap<>();

//        map.put("/dev", "3732");
//        map.put("/run/user/42", "750");
//        map.put("/boot", "281");
//        map.put("/run/user/0", "750");
//        map.put("/home", "83657");
//        map.put("/sys/fs/cgroup", "3748");
//        map.put("/dev/shm", "3748");
//        map.put("/run", "3699");
//        map.put("/", "34605");
        map.put("ALL", "89");

        KPIAgentMessageProtos.KPIAgentMessage.KpiData.GroupKpi groupKpi = KPIAgentMessageProtos.KPIAgentMessage.KpiData.GroupKpi.newBuilder()
                .putAllPairs(map).build();

        AggregatedKpiProtos.AggregatedKpi aggregatedKpi = AggregatedKpiProtos.AggregatedKpi.newBuilder()
                .setAccountId("demo")
                .addAllApplicationId(appList)
                .setInstanceId("0ac94752-9786-4cfe-9239-f0d7426b1c14")
                .setTimeZoneOffsetInSec(19800)
                .addAllServiceId(list)
                .setTimeInGMT(StartTimeInGMT)
                .setKpiData(KPIAgentMessageProtos.KPIAgentMessage.KpiData
                        .newBuilder()
                        .setKpiUid(59)
                        .setTimeInGMT(StartTimeInGMT)
                        .setKpiType(KPIAgentMessageProtos.KPIAgentMessage.KpiData.KpiType.Core)
                        .setKpiName("AVAIL_SIZE_MB")
                        .setGroupKpi(groupKpi)
                        .setCollectionInterval(600)
                        .setIsKpiGroup(true)
                        .build()).build();

        channel.basicPublish("", "static-aggregated-kpi", null, aggregatedKpi.toByteArray());

        System.out.println("Data inserted");
        channel.close();
        connection.close();
    }
}
