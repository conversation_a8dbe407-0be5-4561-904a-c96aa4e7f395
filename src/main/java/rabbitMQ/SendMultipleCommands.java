package rabbitMQ;

import com.appnomic.appsone.common.protbuf.CommandRequestProtos;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.rabbitmq.client.Channel;
import com.rabbitmq.client.Connection;
import com.rabbitmq.client.ConnectionFactory;
import lombok.Data;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class SendMultipleCommands {
    private final static String QUEUE_NAME = "command-messages";

    public static void main(String[] args) {

        try {
            ConnectionFactory factory = new ConnectionFactory();
            factory.setHost("**************");
            factory.setPort(5672);
            factory.useSslProtocol();
            Connection connection = factory.newConnection();
            Channel channel = connection.createChannel();

            channel.queueDeclare(QUEUE_NAME, true, false, false, null);

            ObjectMapper objectMapper = new ObjectMapper();

            ConfigurationJson json = objectMapper.readValue(new File("/home/<USER>/Documents/TestApp/src/main/resources/CommandRequest.json"), ConfigurationJson.class);

            List<String> hostAddresses = json.getHostAddresses();

            for (int i = 0; i < hostAddresses.size(); i++) {
                List<ConfigurationJson.Commands> commands = json.getCommands();

                for (ConfigurationJson.Commands command : commands) {
                    String commandName = command.getName();

                    String producerType = command.getProducerType();

                    Map<String, String> argumentsMap = new HashMap<>();

                    List<ConfigurationJson.Arguments> argumentsList = command.getArguments();

                    for (ConfigurationJson.Arguments argument : argumentsList) {
                        argumentsMap.put(argument.getArgName(), argument.getArgValue());
                    }

                    CommandRequestProtos.Command commandProtos = CommandRequestProtos.Command.newBuilder()
                            .setCommandJobId("0cef1a75-f8f0-426e-9f15-56d0fc36f1162")
                            .setCommandType("ForensicCmds")
                            .setCommand(commandName)
                            .setCommandOutputType("Blob")
                            .setCommandExecType("LongPolling")
                            .putAllArguments(argumentsMap)
                            .setCommandTimeout(180)
                            .setSupervisorCtrlTTL(300)
                            .setRetryNumber(3)
                            .build();
                    CommandRequestProtos.CommandRequest cmddetails = CommandRequestProtos.CommandRequest.newBuilder()
                            .addCommands(commandProtos)
                            .addSupervisorIdentifiers("SupervisorControllerIdentifier")
                            .setAgentType("ForensicAgent")
                            .setAgentIdentifier("e570de02-c585-4917-bbb7-5c97b35e-14")
                            .setTriggerSource("ActionProcessor")
                            .setUserDetailsID("7640123a-fbde-4fe5-9812-581cd1e3a9c1")
                            .setTriggerTime(System.currentTimeMillis())
                            .setViolationTime(System.currentTimeMillis())
                            .putMetadata("AccountId", "d681ef13-d690-4917-jkhg-6c79b-1")
                            .putMetadata("InstanceId", "WEBLOGIC_LOS_App_110_Inst_1")
                            .putMetadata("CategoryId", "5")
                            .putMetadata("ActionId", commandName)
                            .putMetadata("ActionEventTime", System.currentTimeMillis() + "")
                            .putMetadata("IsWorkLoad", "false")
                            .putMetadata("KPIId", "44") //1, 44, 152
                            .putMetadata("KPIAttribute", "ALL")
                            .putMetadata("SupervisorMode", "REMOTE") //REMOTE, LOCAL
                            .putMetadata("ProducerType", producerType) //JDBC or SCRIPT
                            .build();

                    ByteArrayOutputStream os = new ByteArrayOutputStream();
                    cmddetails.writeTo(os);
                    channel.basicPublish("", QUEUE_NAME, null, os.toByteArray());
                    System.out.println("Data Inserted");
                }
            }

            channel.close();
            connection.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}

@Data
class ConfigurationJson {
    List<String> hostAddresses;
    List<Commands> commands;

    @Data
    static class Commands {
        String name;
        String producerType;
        List<Arguments> arguments;
    }

    @Data
    static class Arguments {
        String argName;
        String argValue;
    }
}
